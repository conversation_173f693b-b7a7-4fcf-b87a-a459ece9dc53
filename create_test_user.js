const axios = require('axios');

// Create a test user in Firebase Auth Emulator
async function createTestUser() {
  try {
    console.log('Creating test user in Firebase Auth Emulator...');

    const response = await axios.post('http://127.0.0.1:9099/identitytoolkit.googleapis.com/v1/accounts:signUp?key=fake-api-key', {
      email: '<EMAIL>',
      password: 'password123',
      returnSecureToken: true
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Test user created successfully!');
    console.log('User ID:', response.data.localId);
    console.log('ID Token:', response.data.idToken);

    return {
      uid: response.data.localId,
      idToken: response.data.idToken
    };
  } catch (error) {
    if (error.response) {
      console.log('Error creating user:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
    return null;
  }
}

// Test meal plan generation with authenticated user
async function testMealPlanWithAuth(idToken) {
  try {
    console.log('Testing meal plan generation with authenticated user...');

    const response = await axios.post('http://127.0.0.1:5001/easydietai/us-central1/api/generate-meal-plan', {
      preferences: {
        calorieGoal: 2000,
        mealsPerDay: 3,
        dietType: 'balanced',
        proteinGoal: 150,
        carbsGoal: 250,
        fatGoal: 67,
        dietaryRestrictions: [],
        allergies: [],
        cuisinePreferences: ['عربي']
      },
      duration: 3
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${idToken}`
      },
      timeout: 60000
    });

    console.log('Success! Meal plan generated:');
    console.log(JSON.stringify(response.data, null, 2));
  } catch (error) {
    if (error.response) {
      console.log('Error Response:', error.response.status, error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

// Run the test
async function runTest() {
  const user = await createTestUser();
  if (user) {
    await testMealPlanWithAuth(user.idToken);
  }
}

runTest();
