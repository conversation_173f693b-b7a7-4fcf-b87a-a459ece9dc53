import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/onboarding_provider.dart';

class NotificationsStep extends ConsumerWidget {
  final bool showProfileMessage;

  const NotificationsStep({super.key, this.showProfileMessage = true});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    final data = onboardingState.data;

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإشعارات',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اختر الإشعارات التي تريد تلقيها لمساعدتك في تحقيق أهدافك',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 32),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Meal Reminders
                  _buildNotificationTile(
                    context: context,
                    icon: Icons.restaurant_menu,
                    iconColor: Colors.orange,
                    title: 'تذكير الوجبات',
                    subtitle: 'احصل على تذكير عندما يحين وقت تناول وجباتك',
                    value: data.mealReminders,
                    onChanged: (value) {
                      onboardingNotifier.updateNotifications(
                        mealReminders: value,
                      );
                    },
                  ),

                  const SizedBox(height: 16),

                  // Water Reminders
                  _buildNotificationTile(
                    context: context,
                    icon: Icons.water_drop,
                    iconColor: Colors.blue,
                    title: 'تذكير شرب الماء',
                    subtitle: 'احصل على تذكير لشرب الماء بانتظام',
                    value: data.waterReminders,
                    onChanged: (value) {
                      onboardingNotifier.updateNotifications(
                        waterReminders: value,
                      );
                    },
                  ),

                  const SizedBox(height: 16),

                  // Workout Reminders
                  _buildNotificationTile(
                    context: context,
                    icon: Icons.fitness_center,
                    iconColor: Colors.green,
                    title: 'تذكير التمارين',
                    subtitle: 'احصل على تذكير لممارسة التمارين الرياضية',
                    value: data.workoutReminders,
                    onChanged: (value) {
                      onboardingNotifier.updateNotifications(
                        workoutReminders: value,
                      );
                    },
                  ),

                  const SizedBox(height: 16),

                  // Progress Updates
                  _buildNotificationTile(
                    context: context,
                    icon: Icons.trending_up,
                    iconColor: Colors.purple,
                    title: 'تحديثات التقدم',
                    subtitle: 'احصل على تحديثات حول تقدمك وإنجازاتك',
                    value: data.progressUpdates,
                    onChanged: (value) {
                      onboardingNotifier.updateNotifications(
                        progressUpdates: value,
                      );
                    },
                  ),

                  const SizedBox(height: 32),

                  // Info message - only show when coming from onboarding
                  if (showProfileMessage)
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Theme.of(context).colorScheme.primary,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'يمكنك تغيير هذه الإعدادات لاحقاً من صفحة الملف الشخصي',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationTile({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: iconColor,
          ),
        ],
      ),
    );
  }
}
