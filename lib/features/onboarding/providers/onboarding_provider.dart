import 'dart:math';
import 'package:flutter/widgets.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../core/services/local_storage_service.dart';
import '../../../core/services/firestore_service.dart';
import '../../../shared/models/user_profile.dart';
import '../../../shared/providers/app_state_provider.dart';
import '../../../shared/providers/auth_provider.dart';

part 'onboarding_provider.freezed.dart';
part 'onboarding_provider.g.dart';

/// Onboarding step enum
enum OnboardingStep {
  personalInfo,
  physicalInfo,
  healthInfo,
  goals,
  preferences,
  notifications,
  complete,
}

/// Onboarding form data
@freezed
class OnboardingData with _$OnboardingData {
  const factory OnboardingData({
    // Personal Information
    String? firstName,
    String? lastName,
    DateTime? dateOfBirth,
    @Default(Gender.notSpecified) Gender gender,
    String? phoneNumber,

    // Physical Information
    double? height,
    double? weight,
    @Default(ActivityLevel.moderate) ActivityLevel activityLevel,
    int? dailyBurnedCalories, // manually entered daily burned calories
    @Default(false) bool useManualCalories, // whether to use manual calories instead of activity level
    @Default('metric') String unitSystem,

    // Health Information
    @Default([]) List<String> allergies,
    @Default([]) List<String> dietaryRestrictions,
    @Default([]) List<String> healthConditions,
    @Default([]) List<String> medications,

    // Goals
    @Default(HealthGoal.maintain) HealthGoal healthGoal,
    double? targetWeight,
    int? dailyCalorieGoal,

    // Preferences
    @Default([]) List<String> favoriteIngredients,
    @Default([]) List<String> dislikedIngredients,
    @Default([]) List<String> favoriteCuisines,
    @Default(3) int mealsPerDay,
    @Default(2) int snacksPerDay,

    // Notifications
    @Default(true) bool mealReminders,
    @Default(true) bool waterReminders,
    @Default(true) bool workoutReminders,
    @Default(true) bool progressUpdates,
  }) = _OnboardingData;

  factory OnboardingData.fromJson(Map<String, dynamic> json) =>
      _$OnboardingDataFromJson(json);
}

/// Onboarding state
@freezed
class OnboardingState with _$OnboardingState {
  const factory OnboardingState({
    @Default(OnboardingStep.personalInfo) OnboardingStep currentStep,
    @Default(OnboardingData()) OnboardingData data,
    @Default({}) Map<OnboardingStep, bool> stepValidation,
    @Default({}) Map<String, String> errors,
    @Default(false) bool isLoading,
  }) = _OnboardingState;
}

/// Onboarding notifier
@riverpod
class OnboardingNotifier extends _$OnboardingNotifier {
  @override
  OnboardingState build() {
    // Load saved onboarding data if available
    _loadSavedData();

    // Initialize with personalInfo as the first step and validate all steps
    final initialState = const OnboardingState(
      currentStep: OnboardingStep.personalInfo,
    );

    // Validate all steps on initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _validateAllSteps();
    });

    return initialState;
  }

  /// Load saved onboarding data from local storage
  Future<void> _loadSavedData() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final savedData = localStorageService.loadOnboardingData();

      if (savedData != null) {
        final onboardingData = OnboardingData.fromJson(savedData);
        state = state.copyWith(data: onboardingData);

        // Validate all steps based on loaded data
        _validateAllSteps();
      }
    } catch (e) {
      // If loading fails, continue with default state
    }
  }

  /// Save current onboarding data to local storage
  Future<void> _saveData() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveOnboardingData(state.data.toJson());
    } catch (e) {
      // Handle save error silently
    }
  }

  /// Validate all steps
  void _validateAllSteps() {
    _validatePersonalInfo();
    _validatePhysicalInfo();
    _validateHealthInfo();
    _validateGoals();
    _validatePreferences();
    _validateNotifications();
  }

  /// Go to next step
  void nextStep() {
    final currentIndex = OnboardingStep.values.indexOf(state.currentStep);
    if (currentIndex < OnboardingStep.values.length - 1) {
      final nextStep = OnboardingStep.values[currentIndex + 1];
      state = state.copyWith(currentStep: nextStep);
    }
  }

  /// Go to previous step
  void previousStep() {
    final currentIndex = OnboardingStep.values.indexOf(state.currentStep);
    if (currentIndex > 0) {
      final previousStep = OnboardingStep.values[currentIndex - 1];
      state = state.copyWith(currentStep: previousStep);
    }
  }

  /// Go to specific step
  void goToStep(OnboardingStep step) {
    state = state.copyWith(currentStep: step);

    // Auto-validate steps that don't require user input
    switch (step) {
      case OnboardingStep.healthInfo:
        _validateHealthInfo();
        break;
      case OnboardingStep.preferences:
        _validatePreferences();
        break;
      case OnboardingStep.notifications:
        _validateNotifications();
        break;
      case OnboardingStep.personalInfo:
        _validatePersonalInfo();
        break;
      case OnboardingStep.physicalInfo:
        _validatePhysicalInfo();
        break;
      case OnboardingStep.goals:
        _validateGoals();
        break;
      case OnboardingStep.complete:
        // Complete step doesn't need validation
        break;
    }


  }

  /// Update personal information
  void updatePersonalInfo({
    String? firstName,
    String? lastName,
    DateTime? dateOfBirth,
    Gender? gender,
    String? phoneNumber,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        firstName: firstName ?? state.data.firstName,
        lastName: lastName ?? state.data.lastName,
        dateOfBirth: dateOfBirth ?? state.data.dateOfBirth,
        gender: gender ?? state.data.gender,
        phoneNumber: phoneNumber ?? state.data.phoneNumber,
      ),
    );
    _validatePersonalInfo();
    _saveData(); // Save to local storage
  }

  /// Update physical information
  void updatePhysicalInfo({
    double? height,
    double? weight,
    ActivityLevel? activityLevel,
    int? dailyBurnedCalories,
    bool? useManualCalories,
    String? unitSystem,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        height: height ?? state.data.height,
        weight: weight ?? state.data.weight,
        activityLevel: activityLevel ?? state.data.activityLevel,
        dailyBurnedCalories: dailyBurnedCalories ?? state.data.dailyBurnedCalories,
        useManualCalories: useManualCalories ?? state.data.useManualCalories,
        unitSystem: unitSystem ?? state.data.unitSystem,
      ),
    );
    _validatePhysicalInfo();
    _saveData(); // Save to local storage
  }

  /// Update health information
  void updateHealthInfo({
    List<String>? allergies,
    List<String>? dietaryRestrictions,
    List<String>? healthConditions,
    List<String>? medications,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        allergies: allergies ?? state.data.allergies,
        dietaryRestrictions: dietaryRestrictions ?? state.data.dietaryRestrictions,
        healthConditions: healthConditions ?? state.data.healthConditions,
        medications: medications ?? state.data.medications,
      ),
    );
    _validateHealthInfo();
    _saveData(); // Save to local storage
  }

  /// Update goals
  void updateGoals({
    HealthGoal? healthGoal,
    double? targetWeight,
    int? dailyCalorieGoal,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        healthGoal: healthGoal ?? state.data.healthGoal,
        targetWeight: targetWeight ?? state.data.targetWeight,
        dailyCalorieGoal: dailyCalorieGoal ?? state.data.dailyCalorieGoal,
      ),
    );
    _validateGoals();
    _saveData(); // Save to local storage
  }

  /// Update preferences
  void updatePreferences({
    List<String>? favoriteIngredients,
    List<String>? dislikedIngredients,
    List<String>? favoriteCuisines,
    int? mealsPerDay,
    int? snacksPerDay,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        favoriteIngredients: favoriteIngredients ?? state.data.favoriteIngredients,
        dislikedIngredients: dislikedIngredients ?? state.data.dislikedIngredients,
        favoriteCuisines: favoriteCuisines ?? state.data.favoriteCuisines,
        mealsPerDay: mealsPerDay ?? state.data.mealsPerDay,
        snacksPerDay: snacksPerDay ?? state.data.snacksPerDay,
      ),
    );
    _validatePreferences();
    _saveData(); // Save to local storage
  }

  /// Update notification preferences
  void updateNotifications({
    bool? mealReminders,
    bool? waterReminders,
    bool? workoutReminders,
    bool? progressUpdates,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        mealReminders: mealReminders ?? state.data.mealReminders,
        waterReminders: waterReminders ?? state.data.waterReminders,
        workoutReminders: workoutReminders ?? state.data.workoutReminders,
        progressUpdates: progressUpdates ?? state.data.progressUpdates,
      ),
    );
    _validateNotifications();
    _saveData(); // Save to local storage
  }

  /// Go to complete step (meal plan generation will happen there)
  void goToCompleteStep() {
    state = state.copyWith(currentStep: OnboardingStep.complete);
  }

  /// Complete onboarding (called from complete step after meal plan generation)
  Future<void> completeOnboarding() async {
    state = state.copyWith(isLoading: true);

    try {
      // Mark onboarding as completed in app state
      await ref.read(appStateNotifierProvider.notifier).completeOnboarding();

      // Mark onboarding as completed in local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.setOnboardingCompleted(true);

      // Sync onboarding data to user profile and Firestore
      await _syncOnboardingDataToProfile();

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errors: {'general': 'Failed to complete onboarding'},
      );
    }
  }

  /// Sync onboarding data to user profile and Firestore
  Future<void> _syncOnboardingDataToProfile() async {
    try {
      final authState = ref.read(authNotifierProvider);
      final currentProfile = authState.userProfile;

      // Only sync if user is authenticated
      if (currentProfile == null) {
        return;
      }

      // Create updated profile with onboarding data
      final updatedProfile = currentProfile.copyWith(
        // Personal Information
        firstName: state.data.firstName,
        lastName: state.data.lastName,
        dateOfBirth: state.data.dateOfBirth,
        gender: state.data.gender,
        phoneNumber: state.data.phoneNumber,

        // Physical Information
        height: state.data.height,
        weight: state.data.weight,
        activityLevel: state.data.activityLevel,
        dailyBurnedCalories: state.data.dailyBurnedCalories,
        useManualCalories: state.data.useManualCalories,
        unitSystem: state.data.unitSystem,

        // Health Information
        allergies: state.data.allergies,
        dietaryRestrictions: state.data.dietaryRestrictions,
        healthConditions: state.data.healthConditions,
        medications: state.data.medications,

        // Goals
        healthGoal: state.data.healthGoal,
        targetWeight: state.data.targetWeight,
        dailyCalorieGoal: state.data.dailyCalorieGoal,

        // Preferences
        favoriteIngredients: state.data.favoriteIngredients,
        dislikedIngredients: state.data.dislikedIngredients,
        favoriteCuisines: state.data.favoriteCuisines,
        mealsPerDay: state.data.mealsPerDay,
        snacksPerDay: state.data.snacksPerDay,

        // Notifications
        mealReminders: state.data.mealReminders,
        waterReminders: state.data.waterReminders,
        workoutReminders: state.data.workoutReminders,
        progressUpdates: state.data.progressUpdates,

        // Mark onboarding as completed
        onboardingCompleted: true,
        updatedAt: DateTime.now(),
      );

      // Update auth state with new profile
      await ref.read(authNotifierProvider.notifier).updateUserProfile(updatedProfile);

      // Also sync to Firestore directly to ensure it's saved
      final firestoreService = ref.read(firestoreServiceProvider);
      await firestoreService.saveUserProfile(updatedProfile);

    } catch (e) {
      // Log error but don't throw to avoid breaking onboarding completion
      // The data is still saved locally
    }
  }

  /// Pre-fill onboarding data from user profile (for settings pages)
  void preFillFromUserProfile(UserProfile userProfile) {
    state = state.copyWith(
      data: OnboardingData(
        // Personal Information
        firstName: userProfile.firstName,
        lastName: userProfile.lastName,
        dateOfBirth: userProfile.dateOfBirth,
        gender: userProfile.gender,
        phoneNumber: userProfile.phoneNumber,

        // Physical Information
        height: userProfile.height,
        weight: userProfile.weight,
        activityLevel: userProfile.activityLevel,
        dailyBurnedCalories: userProfile.dailyBurnedCalories,
        useManualCalories: userProfile.useManualCalories,
        unitSystem: userProfile.unitSystem,

        // Health Information
        allergies: userProfile.allergies,
        dietaryRestrictions: userProfile.dietaryRestrictions,
        healthConditions: userProfile.healthConditions,
        medications: userProfile.medications,

        // Goals
        healthGoal: userProfile.healthGoal,
        targetWeight: userProfile.targetWeight,
        dailyCalorieGoal: userProfile.dailyCalorieGoal,

        // Preferences
        favoriteIngredients: userProfile.favoriteIngredients,
        dislikedIngredients: userProfile.dislikedIngredients,
        favoriteCuisines: userProfile.favoriteCuisines,
        mealsPerDay: userProfile.mealsPerDay,
        snacksPerDay: userProfile.snacksPerDay,

        // Notifications
        mealReminders: userProfile.mealReminders,
        waterReminders: userProfile.waterReminders,
        workoutReminders: userProfile.workoutReminders,
        progressUpdates: userProfile.progressUpdates,
      ),
    );

    // Validate all steps based on loaded data
    _validateAllSteps();

    // Save to local storage to keep it in sync
    _saveData();
  }

  /// Pre-fill onboarding data with random test values for testing purposes
  void preFillWithTestData() {
    final random = Random();

    // Sample data arrays
    final firstNames = ['Ahmed', 'Fatima', 'Omar', 'Aisha', 'Khalid', 'Layla', 'Hassan', 'Zeinab', 'Ali', 'Nour'];
    final lastNames = ['Al-Rashid', 'Al-Zahra', 'Al-Mansouri', 'Al-Hashimi', 'Al-Qasimi', 'Al-Maktoum', 'Al-Sabah', 'Al-Thani'];
    final allergies = ['Nuts', 'Dairy', 'Gluten', 'Shellfish', 'Eggs', 'Soy', 'Fish'];
    final dietaryRestrictions = ['Vegetarian', 'Vegan', 'Halal', 'Kosher', 'Low Carb', 'Keto', 'Paleo'];
    final healthConditions = ['Diabetes', 'Hypertension', 'High Cholesterol', 'Thyroid Issues', 'Heart Disease'];
    final medications = ['Metformin', 'Lisinopril', 'Atorvastatin', 'Levothyroxine', 'Aspirin'];
    final favoriteIngredients = ['Chicken', 'Rice', 'Tomatoes', 'Onions', 'Garlic', 'Olive Oil', 'Lemon', 'Parsley'];
    final dislikedIngredients = ['Liver', 'Mushrooms', 'Olives', 'Anchovies', 'Blue Cheese'];
    final favoriteCuisines = ['Arabic', 'Mediterranean', 'Italian', 'Asian', 'Mexican', 'Indian'];

    // Generate random birth date (18-65 years old)
    final now = DateTime.now();
    final minAge = 18;
    final maxAge = 65;
    final birthYear = now.year - (minAge + random.nextInt(maxAge - minAge));
    final birthMonth = 1 + random.nextInt(12);
    final birthDay = 1 + random.nextInt(28); // Safe day range for all months

    // Random physical data
    final height = 150.0 + random.nextDouble() * 50; // 150-200 cm
    final weight = 50.0 + random.nextDouble() * 50; // 50-100 kg
    final useManualCalories = random.nextBool();

    // Random target weight based on health goal
    final healthGoals = HealthGoal.values;
    final selectedHealthGoal = healthGoals[random.nextInt(healthGoals.length)];
    double? targetWeight;
    if (selectedHealthGoal == HealthGoal.loseWeight) {
      targetWeight = weight - (5 + random.nextDouble() * 15); // 5-20 kg less
    } else if (selectedHealthGoal == HealthGoal.gainWeight) {
      targetWeight = weight + (5 + random.nextDouble() * 15); // 5-20 kg more
    }

    // Random lists with 1-3 items each
    final randomAllergies = <String>[];
    final allergyCount = random.nextInt(3);
    for (int i = 0; i < allergyCount; i++) {
      final allergy = allergies[random.nextInt(allergies.length)];
      if (!randomAllergies.contains(allergy)) {
        randomAllergies.add(allergy);
      }
    }

    final randomDietaryRestrictions = <String>[];
    final restrictionCount = random.nextInt(2);
    for (int i = 0; i < restrictionCount; i++) {
      final restriction = dietaryRestrictions[random.nextInt(dietaryRestrictions.length)];
      if (!randomDietaryRestrictions.contains(restriction)) {
        randomDietaryRestrictions.add(restriction);
      }
    }

    final randomHealthConditions = <String>[];
    final conditionCount = random.nextInt(2);
    for (int i = 0; i < conditionCount; i++) {
      final condition = healthConditions[random.nextInt(healthConditions.length)];
      if (!randomHealthConditions.contains(condition)) {
        randomHealthConditions.add(condition);
      }
    }

    final randomMedications = <String>[];
    final medicationCount = random.nextInt(2);
    for (int i = 0; i < medicationCount; i++) {
      final medication = medications[random.nextInt(medications.length)];
      if (!randomMedications.contains(medication)) {
        randomMedications.add(medication);
      }
    }

    final randomFavoriteIngredients = <String>[];
    final favIngredientCount = 3 + random.nextInt(3); // 3-5 items
    for (int i = 0; i < favIngredientCount; i++) {
      final ingredient = favoriteIngredients[random.nextInt(favoriteIngredients.length)];
      if (!randomFavoriteIngredients.contains(ingredient)) {
        randomFavoriteIngredients.add(ingredient);
      }
    }

    final randomDislikedIngredients = <String>[];
    final dislikedIngredientCount = 1 + random.nextInt(3); // 1-3 items
    for (int i = 0; i < dislikedIngredientCount; i++) {
      final ingredient = dislikedIngredients[random.nextInt(dislikedIngredients.length)];
      if (!randomDislikedIngredients.contains(ingredient)) {
        randomDislikedIngredients.add(ingredient);
      }
    }

    final randomFavoriteCuisines = <String>[];
    final cuisineCount = 2 + random.nextInt(3); // 2-4 items
    for (int i = 0; i < cuisineCount; i++) {
      final cuisine = favoriteCuisines[random.nextInt(favoriteCuisines.length)];
      if (!randomFavoriteCuisines.contains(cuisine)) {
        randomFavoriteCuisines.add(cuisine);
      }
    }

    // Update state with all random data
    state = state.copyWith(
      data: OnboardingData(
        // Personal Information
        firstName: firstNames[random.nextInt(firstNames.length)],
        lastName: lastNames[random.nextInt(lastNames.length)],
        dateOfBirth: DateTime(birthYear, birthMonth, birthDay),
        gender: Gender.values[random.nextInt(Gender.values.length)],
        phoneNumber: '+971${50 + random.nextInt(9)}${random.nextInt(1000000).toString().padLeft(7, '0')}',

        // Physical Information
        height: double.parse(height.toStringAsFixed(1)),
        weight: double.parse(weight.toStringAsFixed(1)),
        activityLevel: ActivityLevel.values[random.nextInt(ActivityLevel.values.length)],
        dailyBurnedCalories: useManualCalories ? 1500 + random.nextInt(1000) : null,
        useManualCalories: useManualCalories,
        unitSystem: 'metric',

        // Health Information
        allergies: randomAllergies,
        dietaryRestrictions: randomDietaryRestrictions,
        healthConditions: randomHealthConditions,
        medications: randomMedications,

        // Goals
        healthGoal: selectedHealthGoal,
        targetWeight: targetWeight != null ? double.parse(targetWeight.toStringAsFixed(1)) : null,
        dailyCalorieGoal: 1800 + random.nextInt(800), // 1800-2600 calories

        // Preferences
        favoriteIngredients: randomFavoriteIngredients,
        dislikedIngredients: randomDislikedIngredients,
        favoriteCuisines: randomFavoriteCuisines,
        mealsPerDay: 3 + random.nextInt(2), // 3-4 meals
        snacksPerDay: 1 + random.nextInt(3), // 1-3 snacks

        // Notifications
        mealReminders: random.nextBool(),
        waterReminders: random.nextBool(),
        workoutReminders: random.nextBool(),
        progressUpdates: random.nextBool(),
      ),
    );

    // Validate all steps after pre-filling
    _validateAllSteps();

    // Save the test data
    _saveData();
  }

  /// Validate personal information step
  void _validatePersonalInfo() {
    final errors = <String, String>{};

    if (state.data.firstName == null || state.data.firstName!.isEmpty) {
      errors['firstName'] = 'First name is required';
    }

    if (state.data.lastName == null || state.data.lastName!.isEmpty) {
      errors['lastName'] = 'Last name is required';
    }

    if (state.data.dateOfBirth == null) {
      errors['dateOfBirth'] = 'Date of birth is required';
    } else {
      final age = DateTime.now().year - state.data.dateOfBirth!.year;
      if (age < 13) {
        errors['dateOfBirth'] = 'You must be at least 13 years old';
      }
    }

    if (state.data.gender == Gender.notSpecified) {
      errors['gender'] = 'Please select your gender';
    }

    final isValid = errors.isEmpty;
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.personalInfo: isValid},
      errors: {...state.errors}..removeWhere((key, value) => key.startsWith('personal_')),
    );

    if (errors.isNotEmpty) {
      state = state.copyWith(
        errors: {
          ...state.errors,
          ...errors.map((key, value) => MapEntry('personal_$key', value)),
        },
      );
    }
  }

  /// Validate physical information step
  void _validatePhysicalInfo() {
    final errors = <String, String>{};

    if (state.data.height == null || state.data.height! <= 0) {
      errors['height'] = 'Please enter a valid height';
    } else if (state.data.height! < 100 || state.data.height! > 250) {
      errors['height'] = 'Height must be between 100-250 cm';
    }

    if (state.data.weight == null || state.data.weight! <= 0) {
      errors['weight'] = 'Please enter a valid weight';
    } else if (state.data.weight! < 30 || state.data.weight! > 300) {
      errors['weight'] = 'Weight must be between 30-300 kg';
    }

    // Validate manual calories if that option is selected
    if (state.data.useManualCalories) {
      if (state.data.dailyBurnedCalories == null || state.data.dailyBurnedCalories! <= 0) {
        errors['dailyBurnedCalories'] = 'Please enter valid daily burned calories';
      } else if (state.data.dailyBurnedCalories! < 100 || state.data.dailyBurnedCalories! > 5000) {
        errors['dailyBurnedCalories'] = 'Daily burned calories must be between 100-5000';
      }
    }

    final isValid = errors.isEmpty;
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.physicalInfo: isValid},
      errors: {...state.errors}..removeWhere((key, value) => key.startsWith('physical_')),
    );

    if (errors.isNotEmpty) {
      state = state.copyWith(
        errors: {
          ...state.errors,
          ...errors.map((key, value) => MapEntry('physical_$key', value)),
        },
      );
    }
  }

  /// Validate health information step
  void _validateHealthInfo() {
    // Health info is optional, so always valid
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.healthInfo: true},
    );
  }

  /// Validate goals step
  void _validateGoals() {
    final errors = <String, String>{};

    if (state.data.healthGoal == HealthGoal.loseWeight ||
        state.data.healthGoal == HealthGoal.gainWeight) {
      if (state.data.targetWeight == null || state.data.targetWeight! <= 0) {
        errors['targetWeight'] = 'Please enter your target weight';
      }
    }

    final isValid = errors.isEmpty;
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.goals: isValid},
      errors: {...state.errors}..removeWhere((key, value) => key.startsWith('goals_')),
    );

    if (errors.isNotEmpty) {
      state = state.copyWith(
        errors: {
          ...state.errors,
          ...errors.map((key, value) => MapEntry('goals_$key', value)),
        },
      );
    }
  }

  /// Validate preferences step
  void _validatePreferences() {
    // Preferences are optional, so always valid
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.preferences: true},
    );
  }

  /// Validate notifications step
  void _validateNotifications() {
    // Notifications are optional, so always valid
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.notifications: true},
    );

  }

  /// Check if current step is valid
  bool get isCurrentStepValid {
    return state.stepValidation[state.currentStep] ?? false;
  }

  /// Check if can proceed to next step
  bool get canProceed {
    return isCurrentStepValid && state.currentStep != OnboardingStep.complete;
  }

  /// Get progress percentage
  double get progress {
    final currentIndex = OnboardingStep.values.indexOf(state.currentStep);
    final totalSteps = OnboardingStep.values.length - 1; // Exclude complete step
    return currentIndex / totalSteps;
  }

  /// Clear errors
  void clearErrors() {
    state = state.copyWith(errors: {});
  }

  /// Reset onboarding
  void reset() {
    state = const OnboardingState();
    _clearLocalData();
  }

  /// Clear all local onboarding data
  Future<void> _clearLocalData() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.clearOnboardingData();
      await localStorageService.setOnboardingCompleted(false);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Check if onboarding is completed
  Future<bool> isOnboardingCompleted() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      return localStorageService.isOnboardingCompleted();
    } catch (e) {
      return false;
    }
  }
}
