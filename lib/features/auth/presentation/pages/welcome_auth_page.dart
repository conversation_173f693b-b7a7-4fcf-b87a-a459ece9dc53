import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/router/app_routes.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../../shared/models/user_profile.dart';
import '../../../../shared/providers/app_state_provider.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/widgets/custom_button.dart';

class WelcomeAuthPage extends ConsumerStatefulWidget {
  const WelcomeAuthPage({super.key});

  @override
  ConsumerState<WelcomeAuthPage> createState() => _WelcomeAuthPageState();
}

class _WelcomeAuthPageState extends ConsumerState<WelcomeAuthPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }



  Future<void> _handleGoogleLogin() async {
    setState(() => _isLoading = true);

    try {
      await ref.read(authNotifierProvider.notifier).signInWithGoogle();

      // Sync Firestore data after successful login
      await ref.read(authNotifierProvider.notifier).syncFirestoreData();

      await ref.read(appStateNotifierProvider.notifier).completeWelcome();

      if (mounted) {
        // Check if user has completed profile data in Firestore
        await _navigateBasedOnProfileCompletion();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google sign-in failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Navigate user based on their profile completion status
  Future<void> _navigateBasedOnProfileCompletion() async {
    try {
      final authState = ref.read(authNotifierProvider);
      final userProfile = authState.userProfile;

      if (userProfile == null) {
        // No profile data found, navigate to onboarding
        context.go(AppRoutes.onboarding);
        return;
      }

      // Check if the user has completed their profile
      final isProfileComplete = _isUserProfileComplete(userProfile);

      if (isProfileComplete) {
        // Profile is complete, update local app state and navigate to home
        await ref.read(appStateNotifierProvider.notifier).completeOnboarding();
        context.go(AppRoutes.home);
      } else {
        // Profile is incomplete, navigate to onboarding
        context.go(AppRoutes.onboarding);
      }
    } catch (e) {
      // On error, default to onboarding
      context.go(AppRoutes.onboarding);
    }
  }

  /// Check if user profile has essential data completed
  bool _isUserProfileComplete(UserProfile profile) {
    // Check if onboarding was explicitly marked as completed
    if (profile.onboardingCompleted == true) {
      return true;
    }

    // Check if essential profile data exists
    final hasPersonalInfo = profile.firstName?.isNotEmpty == true &&
                           profile.lastName?.isNotEmpty == true &&
                           profile.dateOfBirth != null &&
                           profile.gender != Gender.notSpecified;

    final hasPhysicalInfo = profile.height != null &&
                           profile.weight != null &&
                           profile.activityLevel != ActivityLevel.moderate; // Default value means not set

    final hasGoals = profile.healthGoal != HealthGoal.maintain && // Default value means not set
                    profile.dailyCalorieGoal != null;

    // Profile is considered complete if it has all essential data
    return hasPersonalInfo && hasPhysicalInfo && hasGoals;
  }

  Future<void> _handleSkip() async {
    setState(() => _isLoading = true);

    try {
      // Mark user as guest and complete welcome
      await ref.read(appStateNotifierProvider.notifier).setGuestUser(true);
      await ref.read(appStateNotifierProvider.notifier).completeWelcome();

      if (mounted) {
        context.go(AppRoutes.onboarding);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Spacer(),

              // Logo and Welcome Text
              FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.restaurant_menu,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 32),
                    Text(
                      l10n.appName,
                      style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'مرحباً بك في تطبيق التغذية الذكي',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اختر طريقة الدخول للمتابعة',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Authentication Options
              SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Google Login Button
                      CustomButton(
                        text: 'تسجيل الدخول بجوجل',
                        onPressed: _isLoading ? null : _handleGoogleLogin,
                        icon: Icon(Icons.g_mobiledata, color: AppColors.textPrimary),
                        backgroundColor: Colors.white,
                        textColor: AppColors.textPrimary,
                        isOutlined: true,
                        isLoading: _isLoading,
                      ),

                      const SizedBox(height: 24),

                      // Divider
                      Row(
                        children: [
                          const Expanded(child: Divider()),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Text(
                              'أو',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ),
                          const Expanded(child: Divider()),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // Skip Button
                      TextButton(
                        onPressed: _isLoading ? null : _handleSkip,
                        child: Text(
                          'تخطي وإنشاء ملف شخصي',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Terms and Privacy
              FadeTransition(
                opacity: _fadeAnimation,
                child: Text(
                  'بالمتابعة، أنت توافق على شروط الاستخدام وسياسة الخصوصية',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
