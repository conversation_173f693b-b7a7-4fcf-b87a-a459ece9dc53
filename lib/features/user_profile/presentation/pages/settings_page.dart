import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../onboarding/providers/onboarding_provider.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'إدارة الخطة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Safe<PERSON><PERSON>(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Page description
              Padding(
                padding: const EdgeInsets.only(bottom: 24.0),
                child: Text(
                  'يمكنك تعديل إعدادات خطتك الشخصية من هنا',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),

              _buildSettingsSection(
                context,
                ref,
                'إعدادات الخطة الشخصية',
                [
                  _buildStepItem(
                    context,
                    ref,
                    'المعلومات الشخصية',
                    'الاسم، تاريخ الميلاد، الجنس، ورقم الهاتف',
                    Icons.person,
                    OnboardingStep.personalInfo,
                  ),
                  _buildStepItem(
                    context,
                    ref,
                    'المعلومات الجسدية',
                    'الطول، الوزن، مستوى النشاط، والسعرات المحروقة',
                    Icons.fitness_center,
                    OnboardingStep.physicalInfo,
                  ),
                  _buildStepItem(
                    context,
                    ref,
                    'المعلومات الصحية',
                    'الحساسية، القيود الغذائية، والحالات الصحية',
                    Icons.health_and_safety,
                    OnboardingStep.healthInfo,
                  ),
                  _buildStepItem(
                    context,
                    ref,
                    'الأهداف',
                    'الهدف الصحي، الوزن المستهدف، والسعرات اليومية',
                    Icons.flag,
                    OnboardingStep.goals,
                  ),
                  _buildStepItem(
                    context,
                    ref,
                    'التفضيلات الغذائية',
                    'المكونات المفضلة، المكونات المكروهة، والمأكولات المفضلة',
                    Icons.restaurant_menu,
                    OnboardingStep.preferences,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context,
    WidgetRef ref,
    String title,
    List<Widget> items,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: AppColors.border.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return Column(
                children: [
                  item,
                  if (index < items.length - 1)
                    Divider(
                      height: 1,
                      color: AppColors.border.withOpacity(0.3),
                      indent: 16,
                      endIndent: 16,
                    ),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildStepItem(
    BuildContext context,
    WidgetRef ref,
    String title,
    String subtitle,
    IconData icon,
    OnboardingStep step,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // Navigate with the step as a query parameter
          context.push('/profile/settings/personal?step=${step.name}');
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Icon container
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.primaryWithOpacity,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                        height: 1.3,
                      ),
                    ),
                  ],
                ),
              ),

              // Arrow
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textSecondary,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
