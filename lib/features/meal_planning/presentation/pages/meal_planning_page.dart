import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../data/models/meal_plan_request.dart';
import '../providers/meal_plan_generation_provider.dart';

class MealPlanningPage extends ConsumerStatefulWidget {
  const MealPlanningPage({super.key});

  @override
  ConsumerState<MealPlanningPage> createState() => _MealPlanningPageState();
}

class _MealPlanningPageState extends ConsumerState<MealPlanningPage> {
  bool _showGenerateForm = false;

  @override
  void initState() {
    super.initState();
    // Load user preferences when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(mealPlanGenerationNotifierProvider.notifier).loadUserPreferences();
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(mealPlanGenerationNotifierProvider);
    final notifier = ref.read(mealPlanGenerationNotifierProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: Text(_showGenerateForm ? 'إنشاء خطة وجبات' : 'خطة الوجبات'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: _showGenerateForm
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => setState(() => _showGenerateForm = false),
            )
          : null,
      ),
      body: _showGenerateForm
        ? _buildGenerateForm(state, notifier)
        : _buildMealPlanningOverview(),
    );
  }

  Widget _buildMealPlanningOverview() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Generate Meal Plan Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'إنشاء خطة وجبات ذكية',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'احصل على خطة وجبات مخصصة لك باستخدام الذكاء الاصطناعي',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => setState(() => _showGenerateForm = true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: AppColors.primary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'إنشاء خطة جديدة',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // My Meal Plans Section
          Text(
            'خططي السابقة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.restaurant_menu_outlined,
                    size: 64,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد خطط وجبات حتى الآن',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ابدأ بإنشاء خطة وجبات جديدة',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateForm(MealPlanGenerationState state, MealPlanGenerationNotifier notifier) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      color: Colors.white,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'خطة وجبات مخصصة بالذكاء الاصطناعي',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'اختر تفضيلاتك واحصل على خطة وجبات مثالية لأهدافك',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Duration Selection
          _buildSectionTitle('مدة الخطة'),
          const SizedBox(height: 12),
          _buildDurationSelector(state, notifier),

          const SizedBox(height: 24),

          // Diet Type Selection
          _buildSectionTitle('نوع النظام الغذائي'),
          const SizedBox(height: 12),
          _buildDietTypeSelector(state, notifier),

          const SizedBox(height: 24),

          // Basic Settings
          _buildSectionTitle('الإعدادات الأساسية'),
          const SizedBox(height: 12),
          _buildBasicSettings(state, notifier),

          const SizedBox(height: 24),

          // Advanced Settings Toggle
          _buildAdvancedSettingsToggle(state, notifier),

          if (state.showAdvancedSettings) ...[
            const SizedBox(height: 16),
            _buildAdvancedSettings(state, notifier),
          ],

          const SizedBox(height: 32),

          // Generate Button
          _buildGenerateButton(state, notifier),

          const SizedBox(height: 16),

          // Error Display
          if (state.error != null) _buildErrorCard(state.error!),

          // Generated Plan Display
          if (state.generatedPlan != null) _buildGeneratedPlanCard(state.generatedPlan!),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildDurationSelector(MealPlanGenerationState state, MealPlanGenerationNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عدد الأيام: ${state.duration}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: state.duration.toDouble(),
                  min: 1,
                  max: 7,
                  divisions: 6,
                  activeColor: AppColors.primary,
                  onChanged: (value) => notifier.updateDuration(value.toInt()),
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('يوم واحد', style: Theme.of(context).textTheme.bodySmall),
              Text('أسبوع', style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDietTypeSelector(MealPlanGenerationState state, MealPlanGenerationNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر نوع النظام الغذائي',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: DietType.values.map((dietType) {
              final isSelected = state.preferences.dietType == dietType;
              return GestureDetector(
                onTap: () => notifier.updateDietType(dietType),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primary : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? AppColors.primary : AppColors.border,
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        dietType.displayName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isSelected ? Colors.white : AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (isSelected) ...[
                        const SizedBox(height: 4),
                        Text(
                          dietType.description,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white70,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicSettings(MealPlanGenerationState state, MealPlanGenerationNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Calorie Goal
          _buildNumberInput(
            'هدف السعرات الحرارية',
            state.preferences.calorieGoal,
            (value) => notifier.updateCalorieGoal(value),
            suffix: 'سعرة',
            min: 1000,
            max: 5000,
          ),
          const SizedBox(height: 16),

          // Meals per day
          _buildNumberInput(
            'عدد الوجبات في اليوم',
            state.preferences.mealsPerDay,
            (value) => notifier.updateMealsPerDay(value),
            suffix: 'وجبة',
            min: 1,
            max: 6,
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedSettingsToggle(MealPlanGenerationState state, MealPlanGenerationNotifier notifier) {
    return GestureDetector(
      onTap: () => notifier.toggleAdvancedSettings(),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.border),
        ),
        child: Row(
          children: [
            Icon(
              state.showAdvancedSettings ? Icons.expand_less : Icons.expand_more,
              color: AppColors.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'إعدادات متقدمة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ),
            if (state.showAdvancedSettings)
              Icon(
                Icons.settings,
                color: AppColors.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNumberInput(
    String label,
    int value,
    Function(int) onChanged, {
    required String suffix,
    required int min,
    required int max,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Slider(
                value: value.toDouble(),
                min: min.toDouble(),
                max: max.toDouble(),
                divisions: (max - min) < 10 ? (max - min) : (max - min) ~/ 10,
                activeColor: AppColors.primary,
                onChanged: (newValue) => onChanged(newValue.toInt()),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$value $suffix',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings(MealPlanGenerationState state, MealPlanGenerationNotifier notifier) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أهداف التغذية المفصلة',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Protein Goal
          _buildNumberInput(
            'هدف البروتين',
            state.preferences.proteinGoal,
            (value) => notifier.updateProteinGoal(value),
            suffix: 'جرام',
            min: 0,
            max: 300,
          ),
          const SizedBox(height: 16),

          // Carbs Goal
          _buildNumberInput(
            'هدف الكربوهيدرات',
            state.preferences.carbsGoal,
            (value) => notifier.updateCarbsGoal(value),
            suffix: 'جرام',
            min: 0,
            max: 500,
          ),
          const SizedBox(height: 16),

          // Fat Goal
          _buildNumberInput(
            'هدف الدهون',
            state.preferences.fatGoal,
            (value) => notifier.updateFatGoal(value),
            suffix: 'جرام',
            min: 0,
            max: 200,
          ),

          const SizedBox(height: 24),

          // Dietary Restrictions
          Text(
            'القيود الغذائية',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildChipSelector(
            items: ['حلال', 'خالي من الجلوتين', 'خالي من اللاكتوز', 'قليل الصوديوم'],
            selectedItems: state.preferences.dietaryRestrictions,
            onAdd: notifier.addDietaryRestriction,
            onRemove: notifier.removeDietaryRestriction,
          ),

          const SizedBox(height: 16),

          // Allergies
          Text(
            'الحساسية',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildChipSelector(
            items: ['المكسرات', 'البيض', 'الأسماك', 'الصويا', 'القمح'],
            selectedItems: state.preferences.allergies,
            onAdd: notifier.addAllergy,
            onRemove: notifier.removeAllergy,
          ),

          const SizedBox(height: 16),

          // Cuisine Preferences
          Text(
            'تفضيلات المطبخ',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildChipSelector(
            items: ['عربي', 'إيطالي', 'آسيوي', 'مكسيكي', 'هندي', 'تركي'],
            selectedItems: state.preferences.cuisinePreferences,
            onAdd: notifier.addCuisinePreference,
            onRemove: notifier.removeCuisinePreference,
          ),
        ],
      ),
    );
  }

  Widget _buildChipSelector({
    required List<String> items,
    required List<String> selectedItems,
    required Function(String) onAdd,
    required Function(String) onRemove,
  }) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: items.map((item) {
        final isSelected = selectedItems.contains(item);
        return GestureDetector(
          onTap: () => isSelected ? onRemove(item) : onAdd(item),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isSelected ? AppColors.primary : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? AppColors.primary : AppColors.border,
              ),
            ),
            child: Text(
              item,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isSelected ? Colors.white : AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGenerateButton(MealPlanGenerationState state, MealPlanGenerationNotifier notifier) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: state.isGenerating ? null : () => notifier.generateMealPlan(),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: state.isGenerating
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'جاري إنشاء الخطة...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.auto_awesome, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'إنشاء خطة الوجبات',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildErrorCard(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حدث خطأ',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  error,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.red.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneratedPlanCard(MealPlanResponse plan) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green.shade600,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'تم إنشاء خطة الوجبات بنجاح!',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            plan.message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.green.shade600,
            ),
          ),
          const SizedBox(height: 16),

          // Plan Summary
          if (plan.mealPlan.days.isNotEmpty) ...[
            Text(
              'ملخص الخطة:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
            const SizedBox(height: 8),
            ...plan.mealPlan.days.take(3).map((day) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.green.shade600,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${day.day}',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'اليوم ${day.day}: ${day.meals.length} وجبات - ${day.totalNutrition.calories} سعرة',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.green.shade600,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ],

          const SizedBox(height: 16),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // TODO: Navigate to detailed plan view
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('سيتم إضافة عرض تفصيلي للخطة قريباً'),
                        backgroundColor: Colors.green.shade600,
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade600,
                    foregroundColor: Colors.white,
                  ),
                  child: Text('عرض التفاصيل'),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: () {
                  // TODO: Save plan
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حفظ الخطة بنجاح'),
                      backgroundColor: Colors.green.shade600,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.green.shade600,
                  side: BorderSide(color: Colors.green.shade600),
                ),
                child: Icon(Icons.bookmark),
              ),
            ],
          ),
        ],
      ),
    );
  }
}