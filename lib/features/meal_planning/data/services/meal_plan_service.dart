import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/services/firebase_functions_service.dart';
import '../models/meal_plan_request.dart';

part 'meal_plan_service.g.dart';

class MealPlanService {
  final FirebaseFunctionsService _functionsService;

  MealPlanService(this._functionsService);

  /// Generate a meal plan using Firebase Functions
  Future<MealPlanResponse> generateMealPlan(MealPlanRequest request) async {
    try {
      final response = await _dioClient.post<Map<String, dynamic>>(
        '/generate-meal-plan',
        data: request.toJson(),
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      return MealPlanResponse.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw Exception('Failed to generate meal plan: $e');
    }
  }

  /// Get user's meal plans
  Future<List<MealPlanResponse>> getUserMealPlans({
    required String userId,
    int limit = 10,
    String status = 'active',
  }) async {
    try {
      final response = await _dioClient.get<Map<String, dynamic>>(
        '/meal-plans/$userId',
        queryParameters: {
          'limit': limit,
          'status': status,
        },
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      final data = response.data!;
      final mealPlansData = data['mealPlans'] as List<dynamic>? ?? [];

      return mealPlansData
          .map((planData) => MealPlanResponse.fromJson(planData as Map<String, dynamic>))
          .toList();
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw Exception('Failed to fetch meal plans: $e');
    }
  }

  /// Analyze nutrition for food items
  Future<NutritionInfo> analyzeNutrition({
    required List<String> foodItems,
    String portion = '1 serving',
  }) async {
    try {
      final response = await _dioClient.post<Map<String, dynamic>>(
        '/analyze-nutrition',
        data: {
          'foodItems': foodItems,
          'portion': portion,
        },
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      final nutritionData = response.data!['nutrition'] as Map<String, dynamic>;
      return NutritionInfo.fromJson(nutritionData);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw Exception('Failed to analyze nutrition: $e');
    }
  }

  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('Connection timeout. Please check your internet connection.');

      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['error'] ?? 'Server error occurred';

        switch (statusCode) {
          case 400:
            return Exception('Invalid request: $message');
          case 401:
            return Exception('Authentication required. Please log in again.');
          case 403:
            return Exception('Access denied: $message');
          case 404:
            return Exception('Service not found');
          case 429:
            return Exception('Too many requests. Please try again later.');
          case 500:
            return Exception('Server error: $message');
          default:
            return Exception('Request failed: $message');
        }

      case DioExceptionType.cancel:
        return Exception('Request was cancelled');

      case DioExceptionType.connectionError:
        return Exception('No internet connection');

      case DioExceptionType.badCertificate:
        return Exception('Security certificate error');

      case DioExceptionType.unknown:
      default:
        return Exception('An unexpected error occurred: ${e.message}');
    }
  }
}

@riverpod
MealPlanService mealPlanService(MealPlanServiceRef ref) {
  final dioClient = ref.watch(dioClientProvider);
  return MealPlanService(dioClient);
}
