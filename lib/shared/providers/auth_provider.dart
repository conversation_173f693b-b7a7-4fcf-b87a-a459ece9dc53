import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../core/services/storage_service.dart';
import '../../core/services/firestore_service.dart';
import '../../core/utils/logger.dart';
import '../../shared/models/user_profile.dart';
import 'app_state_provider.dart';

part 'auth_provider.freezed.dart';
part 'auth_provider.g.dart';

/// Authentication state
@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    User? user,
    UserProfile? userProfile,
    @Default(AuthStatus.initial) AuthStatus status,
    String? errorMessage,
  }) = _AuthState;
}

/// Authentication status enum
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Authentication notifier
@riverpod
class AuthNotifier extends _$AuthNotifier {
  late final FirebaseAuth _firebaseAuth;
  late final GoogleSignIn _googleSignIn;
  late final StorageService _storageService;
  late final FirestoreService _firestoreService;

  @override
  AuthState build() {
    _firebaseAuth = FirebaseAuth.instance;
    _googleSignIn = GoogleSignIn(
      // Client ID for web - automatically uses meta tag from index.html
      // For Android, it uses the google-services.json configuration
      scopes: ['email'],
    );
    _storageService = ref.read(storageServiceProvider);
    _firestoreService = ref.read(firestoreServiceProvider);

    AppLogger.info('AuthNotifier: Initializing auth provider');

    // Listen to auth state changes
    _firebaseAuth.authStateChanges().listen((user) {
      AppLogger.info('AuthNotifier: Auth state changed - User: ${user?.uid ?? 'null'}');
      if (user != null) {
        AppLogger.info('AuthNotifier: User authenticated, loading profile');
        _loadUserProfile(user);
      } else {
        AppLogger.info('AuthNotifier: User not authenticated');
        state = const AuthState(status: AuthStatus.unauthenticated);
      }
    });

    // Check current user on initialization
    final currentUser = _firebaseAuth.currentUser;
    if (currentUser != null) {
      AppLogger.info('AuthNotifier: Found existing user on init: ${currentUser.uid}');
      // Don't call _loadUserProfile here to avoid race condition
      // The authStateChanges listener will handle it
      return AuthState(
        user: currentUser,
        status: AuthStatus.loading,
      );
    }

    AppLogger.info('AuthNotifier: No existing user found');
    return const AuthState(status: AuthStatus.unauthenticated);
  }

  /// Initialize authentication state
  Future<void> initialize() async {
    state = state.copyWith(status: AuthStatus.loading);

    final currentUser = _firebaseAuth.currentUser;
    if (currentUser != null) {
      await _loadUserProfile(currentUser);
    } else {
      state = state.copyWith(status: AuthStatus.unauthenticated);
    }
  }

  /// Load user profile from storage or Firestore
  Future<void> _loadUserProfile(User user) async {
    try {
      AppLogger.info('AuthNotifier: Loading user profile for ${user.uid}');

      UserProfile? userProfile;

      // First, try to load from Firestore (most up-to-date)
      try {
        AppLogger.info('AuthNotifier: Attempting to load profile from Firestore');
        userProfile = await _firestoreService.getUserProfile(user.uid);

        if (userProfile != null) {
          AppLogger.info('AuthNotifier: Found profile in Firestore, caching locally');
          // Cache the Firestore profile locally
          await _storageService.storeUserProfile(userProfile.toJson());
        }
      } catch (firestoreError) {
        AppLogger.warning('AuthNotifier: Failed to load from Firestore: $firestoreError');
        // Continue to try local storage
      }

      // If no Firestore profile, try local storage
      if (userProfile == null) {
        AppLogger.info('AuthNotifier: Trying to load from local storage');
        final cachedProfile = await _storageService.getUserProfile();

        if (cachedProfile != null) {
          AppLogger.info('AuthNotifier: Found cached profile');
          userProfile = UserProfile.fromJson(cachedProfile);

          // Try to sync local profile to Firestore (in background)
          _syncLocalProfileToFirestore(userProfile);
        }
      }

      // If still no profile, create a new one
      if (userProfile == null) {
        AppLogger.info('AuthNotifier: Creating new profile from Firebase user');
        userProfile = UserProfile(
          id: user.uid,
          email: user.email ?? '',
          displayName: user.displayName ?? '',
          photoUrl: user.photoURL,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Cache the new profile locally
        await _storageService.storeUserProfile(userProfile.toJson());

        // Save new profile to Firestore (in background)
        _syncLocalProfileToFirestore(userProfile);

        AppLogger.info('AuthNotifier: New profile created and cached');
      }

      AppLogger.info('AuthNotifier: Setting auth state to authenticated');
      state = state.copyWith(
        user: user,
        userProfile: userProfile,
        status: AuthStatus.authenticated,
        errorMessage: null,
      );

      // Sync local app state if user has completed onboarding
      if (userProfile.onboardingCompleted) {
        AppLogger.info('AuthNotifier: User has completed onboarding, syncing local app state');
        try {
          await ref.read(appStateNotifierProvider.notifier).completeOnboarding();
        } catch (e) {
          AppLogger.warning('AuthNotifier: Failed to sync local app state: $e');
          // Don't throw error as this shouldn't break the login flow
        }
      }

      AppLogger.info('AuthNotifier: Auth state updated - Status: ${state.status}');
    } catch (e) {
      AppLogger.warning('AuthNotifier: Error loading user profile: $e');
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: e.toString(),
      );
    }
  }

  /// Sync local profile to Firestore in background
  void _syncLocalProfileToFirestore(UserProfile profile) {
    // Run in background without blocking the UI
    Future.microtask(() async {
      try {
        AppLogger.info('AuthNotifier: Syncing profile to Firestore in background');
        await _firestoreService.saveUserProfile(profile);
        AppLogger.info('AuthNotifier: Profile synced to Firestore successfully');
      } catch (e) {
        AppLogger.warning('AuthNotifier: Failed to sync profile to Firestore: $e');
        // Don't throw error as this is a background operation
      }
    });
  }

  /// Sign in with email and password
  Future<void> signInWithEmailAndPassword(String email, String password) async {
    try {
      state = state.copyWith(status: AuthStatus.loading);

      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadUserProfile(credential.user!);
      }
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e.code),
      );
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmailAndPassword(String email, String password) async {
    try {
      state = state.copyWith(status: AuthStatus.loading);

      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadUserProfile(credential.user!);
      }
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e.code),
      );
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }

  /// Sign in with Google
  Future<void> signInWithGoogle() async {
    try {
      AppLogger.info('AuthNotifier: Starting Google Sign-In');
      state = state.copyWith(status: AuthStatus.loading);

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        AppLogger.info('AuthNotifier: Google Sign-In cancelled by user');
        state = state.copyWith(status: AuthStatus.unauthenticated);
        return;
      }

      AppLogger.info('AuthNotifier: Google user obtained: ${googleUser.email}');
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      AppLogger.info('AuthNotifier: Signing in with Firebase credential');
      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        AppLogger.info('AuthNotifier: Firebase sign-in successful: ${userCredential.user!.uid}');
        // Don't call _loadUserProfile here - let the authStateChanges listener handle it
        // This prevents race conditions and duplicate calls
      } else {
        AppLogger.warning('AuthNotifier: Firebase sign-in returned null user');
        state = state.copyWith(status: AuthStatus.error, errorMessage: 'Sign-in failed');
      }
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthNotifier: Firebase auth error: ${e.code} - ${e.message}');
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e.code),
      );
    } catch (e) {
      AppLogger.warning('AuthNotifier: Unexpected error during Google Sign-In: $e');
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Sign in as guest
  Future<void> signInAsGuest() async {
    try {
      state = state.copyWith(status: AuthStatus.loading);

      final credential = await _firebaseAuth.signInAnonymously();

      if (credential.user != null) {
        await _loadUserProfile(credential.user!);
      }
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e.code),
      );
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      await _googleSignIn.signOut();
      await _storageService.clearUserData();

      state = const AuthState(status: AuthStatus.unauthenticated);
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Failed to sign out',
      );
    }
  }

  /// Update user profile
  Future<void> updateUserProfile(UserProfile updatedProfile) async {
    try {
      // Update local state
      state = state.copyWith(userProfile: updatedProfile);

      // Cache updated profile locally
      await _storageService.storeUserProfile(updatedProfile.toJson());

      // Sync to Firestore in background
      _syncLocalProfileToFirestore(updatedProfile);

      AppLogger.info('AuthNotifier: User profile updated successfully');
    } catch (e) {
      AppLogger.error('AuthNotifier: Failed to update profile: $e');
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Failed to update profile',
      );
    }
  }

  /// Manually sync Firestore data after login
  /// This can be called from login widgets to ensure data is synced
  Future<void> syncFirestoreData() async {
    try {
      final user = state.user;
      if (user == null) {
        AppLogger.warning('AuthNotifier: Cannot sync Firestore data - no authenticated user');
        return;
      }

      AppLogger.info('AuthNotifier: Manually syncing Firestore data for ${user.uid}');

      // Try to get the latest profile from Firestore
      final firestoreProfile = await _firestoreService.getUserProfile(user.uid);

      if (firestoreProfile != null) {
        AppLogger.info('AuthNotifier: Found updated profile in Firestore, updating local state');

        // Update local state with Firestore data
        state = state.copyWith(userProfile: firestoreProfile);

        // Cache the updated profile locally
        await _storageService.storeUserProfile(firestoreProfile.toJson());

        // Sync local app state if user has completed onboarding
        if (firestoreProfile.onboardingCompleted) {
          AppLogger.info('AuthNotifier: User has completed onboarding, syncing local app state');
          try {
            await ref.read(appStateNotifierProvider.notifier).completeOnboarding();
          } catch (e) {
            AppLogger.warning('AuthNotifier: Failed to sync local app state: $e');
            // Don't throw error as this shouldn't break the sync flow
          }
        }

        AppLogger.info('AuthNotifier: Firestore data synced successfully');
      } else {
        AppLogger.info('AuthNotifier: No profile found in Firestore, syncing local profile to Firestore');

        // If no Firestore profile but we have local profile, sync it to Firestore
        if (state.userProfile != null) {
          await _firestoreService.saveUserProfile(state.userProfile!);
          AppLogger.info('AuthNotifier: Local profile synced to Firestore');
        }
      }
    } catch (e) {
      AppLogger.error('AuthNotifier: Error syncing Firestore data: $e');
      // Don't throw error as this shouldn't break the login flow
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _getErrorMessage(e.code);
    }
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Get user-friendly error message
  String _getErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      default:
        return 'An error occurred. Please try again.';
    }
  }
}

/// Provider for current user
@riverpod
User? currentUser(CurrentUserRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.user;
}

/// Provider for current user profile
@riverpod
UserProfile? currentUserProfile(CurrentUserProfileRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.userProfile;
}

/// Provider for authentication status
@riverpod
AuthStatus authStatus(AuthStatusRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.status;
}

/// Provider for checking if user is authenticated
@riverpod
bool isAuthenticated(IsAuthenticatedRef ref) {
  final authState = ref.watch(authNotifierProvider);
  final isAuth = authState.status == AuthStatus.authenticated;
  AppLogger.info('isAuthenticated: Status=${authState.status}, User=${authState.user?.uid ?? 'null'}, Result=$isAuth');
  return isAuth;
}

/// Provider for checking if user is guest (disabled for demo)
// @riverpod
// bool isGuest(IsGuestRef ref) {
//   final user = ref.watch(currentUserProvider);
//   return user?.isAnonymous ?? false;
// }
