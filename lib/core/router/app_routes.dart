class AppRoutes {
  // Auth Routes
  static const String splash = '/';
  static const String introWizard = '/intro-wizard';
  static const String welcomeAuth = '/welcome-auth';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';

  // Main App Routes
  static const String home = '/home';
  static const String mealDetail = '/home/<USER>';

  // Profile Routes
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String settings = '/profile/settings';
  static const String personalSettings = '/profile/settings/personal';
  static const String notificationsSettings = '/profile/notifications';

  // Meal Planning Routes
  static const String mealPlanning = '/meal-planning';
  static const String generateMealPlan = '/generate-meal-plan';
  static const String createMealPlan = '/meal-planning/create';
  static const String mealPlanDetail = '/meal-planning/detail';

  // Subscription Routes
  static const String subscription = '/subscription';
  static const String subscriptionSuccess = '/subscription/success';

  // Onboarding Routes
  static const String onboarding = '/onboarding';
  static const String personalInfo = '/onboarding/personal-info';
  static const String healthGoals = '/onboarding/health-goals';
  static const String dietaryPreferences = '/onboarding/dietary-preferences';

  // Utility Routes
  static const String notifications = '/notifications';
  static const String help = '/help';
  static const String about = '/about';
  static const String privacyPolicy = '/privacy-policy';
  static const String termsOfService = '/terms-of-service';
}
