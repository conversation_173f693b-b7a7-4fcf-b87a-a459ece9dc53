rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can read and write their own meal plans
    match /meal_plans/{mealPlanId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Users can read and write their own nutrition logs
    match /nutrition_logs/{logId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Users can read and write their own progress data
    match /progress/{progressId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Public read access to food database
    match /foods/{foodId} {
      allow read: if true;
      allow write: if false; // Only admins can write (handled by admin SDK)
    }
    
    // Public read access to recipes
    match /recipes/{recipeId} {
      allow read: if true;
      allow write: if false; // Only admins can write (handled by admin SDK)
    }
  }
}
