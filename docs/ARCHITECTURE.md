# Project Architecture - EasyDietAI

## Directory Structure

- `lib/`
  - `models/`
  - `screens/`
  - `services/`
  - `providers/`
  - `widgets/`

## State Management
Uses Riverpod (`flutter_riverpod`) for reactive and scalable state management.

## Backend
Uses Firebase Functions (Node.js) colocated in `/functions/` with OpenAI API integration.

## HTTP Requests
All network calls use Dio with interceptors and global error handling.

## Localization
Uses `intl` with Arabic as default.
