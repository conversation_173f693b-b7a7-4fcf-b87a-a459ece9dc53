{"version": 3, "file": "index.esm5.js", "sources": ["../src/constants.ts", "../src/assert.ts", "../src/crypt.ts", "../src/deepCopy.ts", "../src/global.ts", "../src/defaults.ts", "../src/deferred.ts", "../src/emulator.ts", "../src/environment.ts", "../src/errors.ts", "../src/json.ts", "../src/jwt.ts", "../src/obj.ts", "../src/promise.ts", "../src/query.ts", "../src/sha1.ts", "../src/subscribe.ts", "../src/validation.ts", "../src/utf8.ts", "../src/uuid.ts", "../src/exponential_backoff.ts", "../src/formatters.ts", "../src/compat.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\n */\n\nexport const CONSTANTS = {\n  /**\n   * @define {boolean} Whether this is the client Node.js SDK.\n   */\n  NODE_CLIENT: false,\n  /**\n   * @define {boolean} Whether this is the Admin Node.js SDK.\n   */\n  NODE_ADMIN: false,\n\n  /**\n   * Firebase SDK Version\n   */\n  SDK_VERSION: '${JSCORE_VERSION}'\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\n\n/**\n * Throws an error if the provided assertion is falsy\n */\nexport const assert = function (assertion: unknown, message: string): void {\n  if (!assertion) {\n    throw assertionError(message);\n  }\n};\n\n/**\n * Returns an Error object suitable for throwing.\n */\nexport const assertionError = function (message: string): Error {\n  return new Error(\n    'Firebase Database (' +\n      CONSTANTS.SDK_VERSION +\n      ') INTERNAL ASSERT FAILED: ' +\n      message\n  );\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nexport function deepCopy<T>(value: T): T {\n  return deepExtend(undefined, value) as T;\n}\n\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nexport function deepExtend(target: unknown, source: unknown): unknown {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source as Date;\n      return new Date(dateValue.getTime());\n\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    (target as Record<string, unknown>)[prop] = deepExtend(\n      (target as Record<string, unknown>)[prop],\n      (source as Record<string, unknown>)[prop]\n    );\n  }\n\n  return target;\n}\n\nfunction isValidKey(key: string): boolean {\n  return key !== '__proto__';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nexport function getGlobal(): typeof globalThis {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { getGlobal } from './global';\n\n/**\n * Keys for experimental properties on the `FirebaseDefaults` object.\n * @public\n */\nexport type ExperimentalKey = 'authTokenSyncURL' | 'authIdTokenMaxAge';\n\n/**\n * An object that can be injected into the environment as __FIREBASE_DEFAULTS__,\n * either as a property of globalThis, a shell environment variable, or a\n * cookie.\n *\n * This object can be used to automatically configure and initialize\n * a Firebase app as well as any emulators.\n *\n * @public\n */\nexport interface FirebaseDefaults {\n  config?: Record<string, string>;\n  emulatorHosts?: Record<string, string>;\n  _authTokenSyncURL?: string;\n  _authIdTokenMaxAge?: number;\n  /**\n   * Override Firebase's runtime environment detection and\n   * force the SDK to act as if it were in the specified environment.\n   */\n  forceEnvironment?: 'browser' | 'node';\n  [key: string]: unknown;\n}\n\ndeclare global {\n  // Need `var` for this to work.\n  // eslint-disable-next-line no-var\n  var __FIREBASE_DEFAULTS__: FirebaseDefaults | undefined;\n}\n\nconst getDefaultsFromGlobal = (): FirebaseDefaults | undefined =>\n  getGlobal().__FIREBASE_DEFAULTS__;\n\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = (): FirebaseDefaults | undefined => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\n\nconst getDefaultsFromCookie = (): FirebaseDefaults | undefined => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nexport const getDefaults = (): FirebaseDefaults | undefined => {\n  try {\n    return (\n      getDefaultsFromGlobal() ||\n      getDefaultsFromEnvVariable() ||\n      getDefaultsFromCookie()\n    );\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nexport const getDefaultEmulatorHost = (\n  productName: string\n): string | undefined => getDefaults()?.emulatorHosts?.[productName];\n\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nexport const getDefaultEmulatorHostnameAndPort = (\n  productName: string\n): [hostname: string, port: number] | undefined => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nexport const getDefaultAppConfig = (): Record<string, string> | undefined =>\n  getDefaults()?.config;\n\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nexport const getExperimentalSetting = <T extends ExperimentalKey>(\n  name: T\n): FirebaseDefaults[`_${T}`] =>\n  getDefaults()?.[`_${name}`] as FirebaseDefaults[`_${T}`];\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class Deferred<R> {\n  promise: Promise<R>;\n  reject: (value?: unknown) => void = () => {};\n  resolve: (value?: unknown) => void = () => {};\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve as (value?: unknown) => void;\n      this.reject = reject as (value?: unknown) => void;\n    });\n  }\n\n  /**\n   * Our API internals are not promiseified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(\n    callback?: (error?: unknown, value?: unknown) => void\n  ): (error: unknown, value?: unknown) => void {\n    return (error, value?) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64urlEncodeWithoutPadding } from './crypt';\n\n// Firebase Auth tokens contain snake_case claims following the JWT standard / convention.\n/* eslint-disable camelcase */\n\nexport type FirebaseSignInProvider =\n  | 'custom'\n  | 'email'\n  | 'password'\n  | 'phone'\n  | 'anonymous'\n  | 'google.com'\n  | 'facebook.com'\n  | 'github.com'\n  | 'twitter.com'\n  | 'microsoft.com'\n  | 'apple.com';\n\ninterface FirebaseIdToken {\n  // Always set to https://securetoken.google.com/PROJECT_ID\n  iss: string;\n\n  // Always set to PROJECT_ID\n  aud: string;\n\n  // The user's unique ID\n  sub: string;\n\n  // The token issue time, in seconds since epoch\n  iat: number;\n\n  // The token expiry time, normally 'iat' + 3600\n  exp: number;\n\n  // The user's unique ID. Must be equal to 'sub'\n  user_id: string;\n\n  // The time the user authenticated, normally 'iat'\n  auth_time: number;\n\n  // The sign in provider, only set when the provider is 'anonymous'\n  provider_id?: 'anonymous';\n\n  // The user's primary email\n  email?: string;\n\n  // The user's email verification status\n  email_verified?: boolean;\n\n  // The user's primary phone number\n  phone_number?: string;\n\n  // The user's display name\n  name?: string;\n\n  // The user's profile photo URL\n  picture?: string;\n\n  // Information on all identities linked to this user\n  firebase: {\n    // The primary sign-in provider\n    sign_in_provider: FirebaseSignInProvider;\n\n    // A map of providers to the user's list of unique identifiers from\n    // each provider\n    identities?: { [provider in FirebaseSignInProvider]?: string[] };\n  };\n\n  // Custom claims set by the developer\n  [claim: string]: unknown;\n\n  uid?: never; // Try to catch a common mistake of \"uid\" (should be \"sub\" instead).\n}\n\nexport type EmulatorMockTokenOptions = ({ user_id: string } | { sub: string }) &\n  Partial<FirebaseIdToken>;\n\nexport function createMockUserToken(\n  token: EmulatorMockTokenOptions,\n  projectId?: string\n): string {\n  if (token.uid) {\n    throw new Error(\n      'The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.'\n    );\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n\n  const payload: FirebaseIdToken = {\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    },\n\n    // Override with user options\n    ...token\n  };\n\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [\n    base64urlEncodeWithoutPadding(JSON.stringify(header)),\n    base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n    signature\n  ].join('.');\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment\n */\nexport function isBrowser(): boolean {\n  return typeof self === 'object' && self.self === self;\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // Typescript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Evaluates a JSON string into a javascript object.\n *\n * @param {string} str A string containing JSON.\n * @return {*} The javascript object representing the specified JSON.\n */\nexport function jsonEval(str: string): unknown {\n  return JSON.parse(str);\n}\n\n/**\n * Returns JSON representing a javascript object.\n * @param {*} data Javascript object to be stringified.\n * @return {string} The JSON contents of the object.\n */\nexport function stringify(data: unknown): string {\n  return JSON.stringify(data);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { jsonEval } from './json';\n\ninterface Claims {\n  [key: string]: {};\n}\n\ninterface DecodedToken {\n  header: object;\n  claims: Claims;\n  data: object;\n  signature: string;\n}\n\n/**\n * Decodes a Firebase auth. token into constituent parts.\n *\n * Notes:\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const decode = function (token: string): DecodedToken {\n  let header = {},\n    claims: Claims = {},\n    data = {},\n    signature = '';\n\n  try {\n    const parts = token.split('.');\n    header = jsonEval(base64Decode(parts[0]) || '') as object;\n    claims = jsonEval(base64Decode(parts[1]) || '') as Claims;\n    signature = parts[2];\n    data = claims['d'] || {};\n    delete claims['d'];\n  } catch (e) {}\n\n  return {\n    header,\n    claims,\n    data,\n    signature\n  };\n};\n\ninterface DecodedToken {\n  header: object;\n  claims: Claims;\n  data: object;\n  signature: string;\n}\n\n/**\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const isValidTimestamp = function (token: string): boolean {\n  const claims: Claims = decode(token).claims;\n  const now: number = Math.floor(new Date().getTime() / 1000);\n  let validSince: number = 0,\n    validUntil: number = 0;\n\n  if (typeof claims === 'object') {\n    if (claims.hasOwnProperty('nbf')) {\n      validSince = claims['nbf'] as number;\n    } else if (claims.hasOwnProperty('iat')) {\n      validSince = claims['iat'] as number;\n    }\n\n    if (claims.hasOwnProperty('exp')) {\n      validUntil = claims['exp'] as number;\n    } else {\n      // token will expire after 24h by default\n      validUntil = validSince + 86400;\n    }\n  }\n\n  return (\n    !!now &&\n    !!validSince &&\n    !!validUntil &&\n    now >= validSince &&\n    now <= validUntil\n  );\n};\n\n/**\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\n *\n * Notes:\n * - May return null if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const issuedAtTime = function (token: string): number | null {\n  const claims: Claims = decode(token).claims;\n  if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\n    return claims['iat'] as number;\n  }\n  return null;\n};\n\n/**\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const isValidFormat = function (token: string): boolean {\n  const decoded = decode(token),\n    claims = decoded.claims;\n\n  return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\n};\n\n/**\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const isAdmin = function (token: string): boolean {\n  const claims: Claims = decode(token).claims;\n  return typeof claims === 'object' && claims['admin'] === true;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function contains<T extends object>(obj: T, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function safeGet<T extends object, K extends keyof T>(\n  obj: T,\n  key: K\n): T[K] | undefined {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\n\nexport function isEmpty(obj: object): obj is {} {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function map<K extends string, V, U>(\n  obj: { [key in K]: V },\n  fn: (value: V, key: K, obj: { [key in K]: V }) => U,\n  contextObj?: unknown\n): { [key in K]: U } {\n  const res: Partial<{ [key in K]: U }> = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res as { [key in K]: U };\n}\n\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nexport function deepEqual(a: object, b: object): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n\n    const aProp = (a as Record<string, unknown>)[k];\n    const bProp = (b as Record<string, unknown>)[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isObject(thing: unknown): thing is object {\n  return thing !== null && typeof thing === 'object';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from './deferred';\n\n/**\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\n * @internal\n */\nexport function promiseWithTimeout<T>(\n  promise: Promise<T>,\n  timeInMS = 2000\n): Promise<T> {\n  const deferredPromise = new Deferred<T>();\n  setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\n  promise.then(deferredPromise.resolve, deferredPromise.reject);\n  return deferredPromise.promise;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\n * params object (e.g. {arg: 'val', arg2: 'val2'})\n * Note: You must prepend it with ? when adding it to a URL.\n */\nexport function querystring(querystringParams: {\n  [key: string]: string | number;\n}): string {\n  const params = [];\n  for (const [key, value] of Object.entries(querystringParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(arrayVal => {\n        params.push(\n          encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal)\n        );\n      });\n    } else {\n      params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n    }\n  }\n  return params.length ? '&' + params.join('&') : '';\n}\n\n/**\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\n * (e.g. {arg: 'val', arg2: 'val2'})\n */\nexport function querystringDecode(querystring: string): Record<string, string> {\n  const obj: Record<string, string> = {};\n  const tokens = querystring.replace(/^\\?/, '').split('&');\n\n  tokens.forEach(token => {\n    if (token) {\n      const [key, value] = token.split('=');\n      obj[decodeURIComponent(key)] = decodeURIComponent(value);\n    }\n  });\n  return obj;\n}\n\n/**\n * Extract the query string part of a URL, including the leading question mark (if present).\n */\nexport function extractQuerystring(url: string): string {\n  const queryStart = url.indexOf('?');\n  if (!queryStart) {\n    return '';\n  }\n  const fragmentStart = url.indexOf('#', queryStart);\n  return url.substring(\n    queryStart,\n    fragmentStart > 0 ? fragmentStart : undefined\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview SHA-1 cryptographic hash.\n * Variable names follow the notation in FIPS PUB 180-3:\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\n *\n * Usage:\n *   var sha1 = new sha1();\n *   sha1.update(bytes);\n *   var hash = sha1.digest();\n *\n * Performance:\n *   Chrome 23:   ~400 Mbit/s\n *   Firefox 16:  ~250 Mbit/s\n *\n */\n\n/**\n * SHA-1 cryptographic hash constructor.\n *\n * The properties declared here are discussed in the above algorithm document.\n * @constructor\n * @final\n * @struct\n */\nexport class Sha1 {\n  /**\n   * Holds the previous values of accumulated variables a-e in the compress_\n   * function.\n   * @private\n   */\n  private chain_: number[] = [];\n\n  /**\n   * A buffer holding the partially computed hash result.\n   * @private\n   */\n  private buf_: number[] = [];\n\n  /**\n   * An array of 80 bytes, each a part of the message to be hashed.  Referred to\n   * as the message schedule in the docs.\n   * @private\n   */\n  private W_: number[] = [];\n\n  /**\n   * Contains data needed to pad messages less than 64 bytes.\n   * @private\n   */\n  private pad_: number[] = [];\n\n  /**\n   * @private {number}\n   */\n  private inbuf_: number = 0;\n\n  /**\n   * @private {number}\n   */\n  private total_: number = 0;\n\n  blockSize: number;\n\n  constructor() {\n    this.blockSize = 512 / 8;\n\n    this.pad_[0] = 128;\n    for (let i = 1; i < this.blockSize; ++i) {\n      this.pad_[i] = 0;\n    }\n\n    this.reset();\n  }\n\n  reset(): void {\n    this.chain_[0] = 0x67452301;\n    this.chain_[1] = 0xefcdab89;\n    this.chain_[2] = 0x98badcfe;\n    this.chain_[3] = 0x10325476;\n    this.chain_[4] = 0xc3d2e1f0;\n\n    this.inbuf_ = 0;\n    this.total_ = 0;\n  }\n\n  /**\n   * Internal compress helper function.\n   * @param buf Block to compress.\n   * @param offset Offset of the block in the buffer.\n   * @private\n   */\n  compress_(buf: number[] | Uint8Array | string, offset?: number): void {\n    if (!offset) {\n      offset = 0;\n    }\n\n    const W = this.W_;\n\n    // get 16 big endian words\n    if (typeof buf === 'string') {\n      for (let i = 0; i < 16; i++) {\n        // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\n        // have a bug that turns the post-increment ++ operator into pre-increment\n        // during JIT compilation.  We have code that depends heavily on SHA-1 for\n        // correctness and which is affected by this bug, so I've removed all uses\n        // of post-increment ++ in which the result value is used.  We can revert\n        // this change once the Safari bug\n        // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\n        // most clients have been updated.\n        W[i] =\n          (buf.charCodeAt(offset) << 24) |\n          (buf.charCodeAt(offset + 1) << 16) |\n          (buf.charCodeAt(offset + 2) << 8) |\n          buf.charCodeAt(offset + 3);\n        offset += 4;\n      }\n    } else {\n      for (let i = 0; i < 16; i++) {\n        W[i] =\n          (buf[offset] << 24) |\n          (buf[offset + 1] << 16) |\n          (buf[offset + 2] << 8) |\n          buf[offset + 3];\n        offset += 4;\n      }\n    }\n\n    // expand to 80 words\n    for (let i = 16; i < 80; i++) {\n      const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n      W[i] = ((t << 1) | (t >>> 31)) & 0xffffffff;\n    }\n\n    let a = this.chain_[0];\n    let b = this.chain_[1];\n    let c = this.chain_[2];\n    let d = this.chain_[3];\n    let e = this.chain_[4];\n    let f, k;\n\n    // TODO(user): Try to unroll this loop to speed up the computation.\n    for (let i = 0; i < 80; i++) {\n      if (i < 40) {\n        if (i < 20) {\n          f = d ^ (b & (c ^ d));\n          k = 0x5a827999;\n        } else {\n          f = b ^ c ^ d;\n          k = 0x6ed9eba1;\n        }\n      } else {\n        if (i < 60) {\n          f = (b & c) | (d & (b | c));\n          k = 0x8f1bbcdc;\n        } else {\n          f = b ^ c ^ d;\n          k = 0xca62c1d6;\n        }\n      }\n\n      const t = (((a << 5) | (a >>> 27)) + f + e + k + W[i]) & 0xffffffff;\n      e = d;\n      d = c;\n      c = ((b << 30) | (b >>> 2)) & 0xffffffff;\n      b = a;\n      a = t;\n    }\n\n    this.chain_[0] = (this.chain_[0] + a) & 0xffffffff;\n    this.chain_[1] = (this.chain_[1] + b) & 0xffffffff;\n    this.chain_[2] = (this.chain_[2] + c) & 0xffffffff;\n    this.chain_[3] = (this.chain_[3] + d) & 0xffffffff;\n    this.chain_[4] = (this.chain_[4] + e) & 0xffffffff;\n  }\n\n  update(bytes?: number[] | Uint8Array | string, length?: number): void {\n    // TODO(johnlenz): tighten the function signature and remove this check\n    if (bytes == null) {\n      return;\n    }\n\n    if (length === undefined) {\n      length = bytes.length;\n    }\n\n    const lengthMinusBlock = length - this.blockSize;\n    let n = 0;\n    // Using local instead of member variables gives ~5% speedup on Firefox 16.\n    const buf = this.buf_;\n    let inbuf = this.inbuf_;\n\n    // The outer while loop should execute at most twice.\n    while (n < length) {\n      // When we have no data in the block to top up, we can directly process the\n      // input buffer (assuming it contains sufficient data). This gives ~25%\n      // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\n      // the data is provided in large chunks (or in multiples of 64 bytes).\n      if (inbuf === 0) {\n        while (n <= lengthMinusBlock) {\n          this.compress_(bytes, n);\n          n += this.blockSize;\n        }\n      }\n\n      if (typeof bytes === 'string') {\n        while (n < length) {\n          buf[inbuf] = bytes.charCodeAt(n);\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      } else {\n        while (n < length) {\n          buf[inbuf] = bytes[n];\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      }\n    }\n\n    this.inbuf_ = inbuf;\n    this.total_ += length;\n  }\n\n  /** @override */\n  digest(): number[] {\n    const digest: number[] = [];\n    let totalBits = this.total_ * 8;\n\n    // Add pad 0x80 0x00*.\n    if (this.inbuf_ < 56) {\n      this.update(this.pad_, 56 - this.inbuf_);\n    } else {\n      this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\n    }\n\n    // Add # bits.\n    for (let i = this.blockSize - 1; i >= 56; i--) {\n      this.buf_[i] = totalBits & 255;\n      totalBits /= 256; // Don't use bit-shifting here!\n    }\n\n    this.compress_(this.buf_);\n\n    let n = 0;\n    for (let i = 0; i < 5; i++) {\n      for (let j = 24; j >= 0; j -= 8) {\n        digest[n] = (this.chain_[i] >> j) & 255;\n        ++n;\n      }\n    }\n    return digest;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport type NextFn<T> = (value: T) => void;\nexport type ErrorFn = (error: Error) => void;\nexport type CompleteFn = () => void;\n\nexport interface Observer<T> {\n  // Called once for each value in a stream of values.\n  next: NextFn<T>;\n\n  // A stream terminates by a single call to EITHER error() or complete().\n  error: ErrorFn;\n\n  // No events will be sent to next() once complete() is called.\n  complete: CompleteFn;\n}\n\nexport type PartialObserver<T> = Partial<Observer<T>>;\n\n// TODO: Support also Unsubscribe.unsubscribe?\nexport type Unsubscribe = () => void;\n\n/**\n * The Subscribe interface has two forms - passing the inline function\n * callbacks, or a object interface with callback properties.\n */\nexport interface Subscribe<T> {\n  (next?: NextFn<T>, error?: ErrorFn, complete?: CompleteFn): Unsubscribe;\n  (observer: PartialObserver<T>): Unsubscribe;\n}\n\nexport interface Observable<T> {\n  // Subscribe method\n  subscribe: Subscribe<T>;\n}\n\nexport type Executor<T> = (observer: Observer<T>) => void;\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nexport function createSubscribe<T>(\n  executor: Executor<T>,\n  onNoObservers?: Executor<T>\n): Subscribe<T> {\n  const proxy = new ObserverProxy<T>(executor, onNoObservers);\n  return proxy.subscribe.bind(proxy);\n}\n\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy<T> implements Observer<T> {\n  private observers: Array<Observer<T>> | undefined = [];\n  private unsubscribes: Unsubscribe[] = [];\n  private onNoObservers: Executor<T> | undefined;\n  private observerCount = 0;\n  // Micro-task scheduling by calling task.then().\n  private task = Promise.resolve();\n  private finalized = false;\n  private finalError?: Error;\n\n  /**\n   * @param executor Function which can make calls to a single Observer\n   *     as a proxy.\n   * @param onNoObservers Callback when count of Observers goes to zero.\n   */\n  constructor(executor: Executor<T>, onNoObservers?: Executor<T>) {\n    this.onNoObservers = onNoObservers;\n    // Call the executor asynchronously so subscribers that are called\n    // synchronously after the creation of the subscribe function\n    // can still receive the very first value generated in the executor.\n    this.task\n      .then(() => {\n        executor(this);\n      })\n      .catch(e => {\n        this.error(e);\n      });\n  }\n\n  next(value: T): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.next(value);\n    });\n  }\n\n  error(error: Error): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.error(error);\n    });\n    this.close(error);\n  }\n\n  complete(): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.complete();\n    });\n    this.close();\n  }\n\n  /**\n   * Subscribe function that can be used to add an Observer to the fan-out list.\n   *\n   * - We require that no event is sent to a subscriber sychronously to their\n   *   call to subscribe().\n   */\n  subscribe(\n    nextOrObserver?: NextFn<T> | PartialObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ): Unsubscribe {\n    let observer: Observer<T>;\n\n    if (\n      nextOrObserver === undefined &&\n      error === undefined &&\n      complete === undefined\n    ) {\n      throw new Error('Missing Observer.');\n    }\n\n    // Assemble an Observer object when passed as callback functions.\n    if (\n      implementsAnyMethods(nextOrObserver as { [key: string]: unknown }, [\n        'next',\n        'error',\n        'complete'\n      ])\n    ) {\n      observer = nextOrObserver as Observer<T>;\n    } else {\n      observer = {\n        next: nextOrObserver as NextFn<T>,\n        error,\n        complete\n      } as Observer<T>;\n    }\n\n    if (observer.next === undefined) {\n      observer.next = noop as NextFn<T>;\n    }\n    if (observer.error === undefined) {\n      observer.error = noop as ErrorFn;\n    }\n    if (observer.complete === undefined) {\n      observer.complete = noop as CompleteFn;\n    }\n\n    const unsub = this.unsubscribeOne.bind(this, this.observers!.length);\n\n    // Attempt to subscribe to a terminated Observable - we\n    // just respond to the Observer with the final error or complete\n    // event.\n    if (this.finalized) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.task.then(() => {\n        try {\n          if (this.finalError) {\n            observer.error(this.finalError);\n          } else {\n            observer.complete();\n          }\n        } catch (e) {\n          // nothing\n        }\n        return;\n      });\n    }\n\n    this.observers!.push(observer as Observer<T>);\n\n    return unsub;\n  }\n\n  // Unsubscribe is synchronous - we guarantee that no events are sent to\n  // any unsubscribed Observer.\n  private unsubscribeOne(i: number): void {\n    if (this.observers === undefined || this.observers[i] === undefined) {\n      return;\n    }\n\n    delete this.observers[i];\n\n    this.observerCount -= 1;\n    if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n      this.onNoObservers(this);\n    }\n  }\n\n  private forEachObserver(fn: (observer: Observer<T>) => void): void {\n    if (this.finalized) {\n      // Already closed by previous event....just eat the additional values.\n      return;\n    }\n\n    // Since sendOne calls asynchronously - there is no chance that\n    // this.observers will become undefined.\n    for (let i = 0; i < this.observers!.length; i++) {\n      this.sendOne(i, fn);\n    }\n  }\n\n  // Call the Observer via one of it's callback function. We are careful to\n  // confirm that the observe has not been unsubscribed since this asynchronous\n  // function had been queued.\n  private sendOne(i: number, fn: (observer: Observer<T>) => void): void {\n    // Execute the callback asynchronously\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      if (this.observers !== undefined && this.observers[i] !== undefined) {\n        try {\n          fn(this.observers[i]);\n        } catch (e) {\n          // Ignore exceptions raised in Observers or missing methods of an\n          // Observer.\n          // Log error to console. b/31404806\n          if (typeof console !== 'undefined' && console.error) {\n            console.error(e);\n          }\n        }\n      }\n    });\n  }\n\n  private close(err?: Error): void {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    if (err !== undefined) {\n      this.finalError = err;\n    }\n    // Proxy is no longer needed - garbage collect references\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      this.observers = undefined;\n      this.onNoObservers = undefined;\n    });\n  }\n}\n\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(fn: Function, onError?: ErrorFn): Function {\n  return (...args: unknown[]) => {\n    Promise.resolve(true)\n      .then(() => {\n        fn(...args);\n      })\n      .catch((error: Error) => {\n        if (onError) {\n          onError(error);\n        }\n      });\n  };\n}\n\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(\n  obj: { [key: string]: unknown },\n  methods: string[]\n): boolean {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n\n  for (const method of methods) {\n    if (method in obj && typeof obj[method] === 'function') {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction noop(): void {\n  // do nothing\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Check to make sure the appropriate number of arguments are provided for a public function.\n * Throws an error if it fails.\n *\n * @param fnName The function name\n * @param minCount The minimum number of arguments to allow for the function call\n * @param maxCount The maximum number of argument to allow for the function call\n * @param argCount The actual number of arguments provided.\n */\nexport const validateArgCount = function (\n  fnName: string,\n  minCount: number,\n  maxCount: number,\n  argCount: number\n): void {\n  let argError;\n  if (argCount < minCount) {\n    argError = 'at least ' + minCount;\n  } else if (argCount > maxCount) {\n    argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\n  }\n  if (argError) {\n    const error =\n      fnName +\n      ' failed: Was called with ' +\n      argCount +\n      (argCount === 1 ? ' argument.' : ' arguments.') +\n      ' Expects ' +\n      argError +\n      '.';\n    throw new Error(error);\n  }\n};\n\n/**\n * Generates a string to prefix an error message about failed argument validation\n *\n * @param fnName The function name\n * @param argName The name of the argument\n * @return The prefix to add to the error thrown for validation.\n */\nexport function errorPrefix(fnName: string, argName: string): string {\n  return `${fnName} failed: ${argName} argument `;\n}\n\n/**\n * @param fnName\n * @param argumentNumber\n * @param namespace\n * @param optional\n */\nexport function validateNamespace(\n  fnName: string,\n  namespace: string,\n  optional: boolean\n): void {\n  if (optional && !namespace) {\n    return;\n  }\n  if (typeof namespace !== 'string') {\n    //TODO: I should do more validation here. We only allow certain chars in namespaces.\n    throw new Error(\n      errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.'\n    );\n  }\n}\n\nexport function validateCallback(\n  fnName: string,\n  argumentName: string,\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  callback: Function,\n  optional: boolean\n): void {\n  if (optional && !callback) {\n    return;\n  }\n  if (typeof callback !== 'function') {\n    throw new Error(\n      errorPrefix(fnName, argumentName) + 'must be a valid function.'\n    );\n  }\n}\n\nexport function validateContextObject(\n  fnName: string,\n  argumentName: string,\n  context: unknown,\n  optional: boolean\n): void {\n  if (optional && !context) {\n    return;\n  }\n  if (typeof context !== 'object' || context === null) {\n    throw new Error(\n      errorPrefix(fnName, argumentName) + 'must be a valid context object.'\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert } from './assert';\n\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\n// so it's been modified.\n\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\n// use 2 characters in Javascript.  All 4-byte UTF-8 characters begin with a first\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\n// pair).\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\n\n/**\n * @param {string} str\n * @return {Array}\n */\nexport const stringToByteArray = function (str: string): number[] {\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n\n    // Is this the lead surrogate in a surrogate pair?\n    if (c >= 0xd800 && c <= 0xdbff) {\n      const high = c - 0xd800; // the high 10 bits.\n      i++;\n      assert(i < str.length, 'Surrogate pair missing trail surrogate.');\n      const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\n      c = 0x10000 + (high << 10) + low;\n    }\n\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (c < 65536) {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Calculate length without actually converting; useful for doing cheaper validation.\n * @param {string} str\n * @return {number}\n */\nexport const stringLength = function (str: string): number {\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i);\n    if (c < 128) {\n      p++;\n    } else if (c < 2048) {\n      p += 2;\n    } else if (c >= 0xd800 && c <= 0xdbff) {\n      // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\n      p += 4;\n      i++; // skip trail surrogate.\n    } else {\n      p += 3;\n    }\n  }\n  return p;\n};\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Copied from https://stackoverflow.com/a/2117523\n * Generates a new uuid.\n * @public\n */\nexport const uuidv4 = function (): string {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (Math.random() * 16) | 0,\n      v = c === 'x' ? r : (r & 0x3) | 0x8;\n    return v.toString(16);\n  });\n};\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nexport const MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nexport const RANDOM_FACTOR = 0.5;\n\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nexport function calculateBackoffMillis(\n  backoffCount: number,\n  intervalMillis: number = DEFAULT_INTERVAL_MILLIS,\n  backoffFactor: number = DEFAULT_BACKOFF_FACTOR\n): number {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n    // A fraction of the backoff value to add/subtract.\n    // Deviation: changes multiplication order to improve readability.\n    RANDOM_FACTOR *\n      currBaseValue *\n      // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n      // if we add or subtract.\n      (Math.random() - 0.5) *\n      2\n  );\n\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Provide English ordinal letters after a number\n */\nexport function ordinal(i: number): string {\n  if (!Number.isFinite(i)) {\n    return `${i}`;\n  }\n  return i + indicator(i);\n}\n\nfunction indicator(i: number): string {\n  i = Math.abs(i);\n  const cent = i % 100;\n  if (cent >= 10 && cent <= 20) {\n    return 'th';\n  }\n  const dec = i % 10;\n  if (dec === 1) {\n    return 'st';\n  }\n  if (dec === 2) {\n    return 'nd';\n  }\n  if (dec === 3) {\n    return 'rd';\n  }\n  return 'th';\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n"], "names": ["stringToByteArray"], "mappings": ";;AAAA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AAEU,IAAA,SAAS,GAAG;AACvB;;AAEG;AACH,IAAA,WAAW,EAAE,KAAK;AAClB;;AAEG;AACH,IAAA,UAAU,EAAE,KAAK;AAEjB;;AAEG;AACH,IAAA,WAAW,EAAE,mBAAmB;;;AClClC;;;;;;;;;;;;;;;AAeG;AAIH;;AAEG;AACU,IAAA,MAAM,GAAG,UAAU,SAAkB,EAAE,OAAe,EAAA;IACjE,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,cAAc,CAAC,OAAO,CAAC,CAAC;AAC/B,KAAA;AACH,EAAE;AAEF;;AAEG;AACI,IAAM,cAAc,GAAG,UAAU,OAAe,EAAA;IACrD,OAAO,IAAI,KAAK,CACd,qBAAqB;AACnB,QAAA,SAAS,CAAC,WAAW;QACrB,4BAA4B;AAC5B,QAAA,OAAO,CACV,CAAC;AACJ;;ACtCA;;;;;;;;;;;;;;;AAeG;AAEH,IAAMA,mBAAiB,GAAG,UAAU,GAAW,EAAA;;IAE7C,IAAM,GAAG,GAAa,EAAE,CAAC;IACzB,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,GAAG,GAAG,EAAE;AACX,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACd,SAAA;aAAM,IAAI,CAAC,GAAG,IAAI,EAAE;AACnB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;AAC1B,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC3B,SAAA;AAAM,aAAA,IACL,CAAC,CAAC,GAAG,MAAM,MAAM,MAAM;AACvB,YAAA,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM;AAClB,YAAA,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,MAAM,MAAM,EAC3C;;YAEA,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AACpE,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AAC3B,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC;AAClC,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AACjC,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC3B,SAAA;AAAM,aAAA;AACL,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AAC3B,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AACjC,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC3B,SAAA;AACF,KAAA;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEF;;;;;AAKG;AACH,IAAM,iBAAiB,GAAG,UAAU,KAAe,EAAA;;IAEjD,IAAM,GAAG,GAAa,EAAE,CAAC;AACzB,IAAA,IAAI,GAAG,GAAG,CAAC,EACT,CAAC,GAAG,CAAC,CAAC;AACR,IAAA,OAAO,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE;AACzB,QAAA,IAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;QACxB,IAAI,EAAE,GAAG,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;AACpC,SAAA;AAAM,aAAA,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE;AAC/B,YAAA,IAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;YACxB,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC9D,SAAA;AAAM,aAAA,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE;;AAE/B,YAAA,IAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACxB,YAAA,IAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACxB,YAAA,IAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACxB,YAAA,IAAM,CAAC,GACL,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AACpE,gBAAA,OAAO,CAAC;AACV,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACnD,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACrD,SAAA;AAAM,aAAA;AACL,YAAA,IAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACxB,YAAA,IAAM,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;AACxB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,CAC5B,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CACjD,CAAC;AACH,SAAA;AACF,KAAA;AACD,IAAA,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB,CAAC,CAAC;AAkBF;AACA;AACA;AACa,IAAA,MAAM,GAAW;AAC5B;;AAEG;AACH,IAAA,cAAc,EAAE,IAAI;AAEpB;;AAEG;AACH,IAAA,cAAc,EAAE,IAAI;AAEpB;;;AAGG;AACH,IAAA,qBAAqB,EAAE,IAAI;AAE3B;;;AAGG;AACH,IAAA,qBAAqB,EAAE,IAAI;AAE3B;;;AAGG;AACH,IAAA,iBAAiB,EACf,4BAA4B,GAAG,4BAA4B,GAAG,YAAY;AAE5E;;AAEG;AACH,IAAA,IAAI,YAAY,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;KACvC;AAED;;AAEG;AACH,IAAA,IAAI,oBAAoB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;KACvC;AAED;;;;;;AAMG;AACH,IAAA,kBAAkB,EAAE,OAAO,IAAI,KAAK,UAAU;AAE9C;;;;;;;;AAQG;AACH,IAAA,eAAe,EAAf,UAAgB,KAA4B,EAAE,OAAiB,EAAA;AAC7D,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACzB,YAAA,MAAM,KAAK,CAAC,+CAA+C,CAAC,CAAC;AAC9D,SAAA;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAM,aAAa,GAAG,OAAO;cACzB,IAAI,CAAC,qBAAsB;AAC7B,cAAE,IAAI,CAAC,cAAe,CAAC;QAEzB,IAAM,MAAM,GAAG,EAAE,CAAC;AAElB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACvB,IAAM,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AACvC,YAAA,IAAM,KAAK,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAM,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AACvC,YAAA,IAAM,KAAK,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAE3C,YAAA,IAAM,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC;AAC5B,YAAA,IAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;AACtD,YAAA,IAAI,QAAQ,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;AACpD,YAAA,IAAI,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC;YAE5B,IAAI,CAAC,SAAS,EAAE;gBACd,QAAQ,GAAG,EAAE,CAAC;gBAEd,IAAI,CAAC,SAAS,EAAE;oBACd,QAAQ,GAAG,EAAE,CAAC;AACf,iBAAA;AACF,aAAA;YAED,MAAM,CAAC,IAAI,CACT,aAAa,CAAC,QAAQ,CAAC,EACvB,aAAa,CAAC,QAAQ,CAAC,EACvB,aAAa,CAAC,QAAQ,CAAC,EACvB,aAAa,CAAC,QAAQ,CAAC,CACxB,CAAC;AACH,SAAA;AAED,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACxB;AAED;;;;;;;AAOG;AACH,IAAA,YAAY,EAAZ,UAAa,KAAa,EAAE,OAAiB,EAAA;;;AAG3C,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,OAAO,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;AACpB,SAAA;QACD,OAAO,IAAI,CAAC,eAAe,CAACA,mBAAiB,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;KAChE;AAED;;;;;;;AAOG;AACH,IAAA,YAAY,EAAZ,UAAa,KAAa,EAAE,OAAgB,EAAA;;;AAG1C,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,OAAO,EAAE;AACvC,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;AACpB,SAAA;QACD,OAAO,iBAAiB,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;KACxE;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,uBAAuB,EAAvB,UAAwB,KAAa,EAAE,OAAgB,EAAA;QACrD,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAM,aAAa,GAAG,OAAO;cACzB,IAAI,CAAC,qBAAsB;AAC7B,cAAE,IAAI,CAAC,cAAe,CAAC;QAEzB,IAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAI;AAClC,YAAA,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAE/C,YAAA,IAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AACnC,YAAA,IAAM,KAAK,GAAG,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7D,YAAA,EAAE,CAAC,CAAC;AAEJ,YAAA,IAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AACnC,YAAA,IAAM,KAAK,GAAG,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC9D,YAAA,EAAE,CAAC,CAAC;AAEJ,YAAA,IAAM,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AACnC,YAAA,IAAM,KAAK,GAAG,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC9D,YAAA,EAAE,CAAC,CAAC;AAEJ,YAAA,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBACpE,MAAM,IAAI,uBAAuB,EAAE,CAAC;AACrC,aAAA;AAED,YAAA,IAAM,QAAQ,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;AAC7C,YAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEtB,IAAI,KAAK,KAAK,EAAE,EAAE;AAChB,gBAAA,IAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;AACtD,gBAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEtB,IAAI,KAAK,KAAK,EAAE,EAAE;AAChB,oBAAA,IAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC;AAC/C,oBAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACvB,iBAAA;AACF,aAAA;AACF,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;AAIG;IACH,KAAK,EAAA,YAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;AAChC,YAAA,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;;AAGhC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,gBAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACrD,gBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAChD,gBAAA,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpE,gBAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;;AAG9D,gBAAA,IAAI,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AACtC,oBAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7D,oBAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7D,iBAAA;AACF,aAAA;AACF,SAAA;KACF;EACD;AAEF;;AAEG;AACH,IAAA,uBAAA,kBAAA,UAAA,MAAA,EAAA;IAA6C,SAAK,CAAA,uBAAA,EAAA,MAAA,CAAA,CAAA;AAAlD,IAAA,SAAA,uBAAA,GAAA;QAAA,IAEC,KAAA,GAAA,MAAA,KAAA,IAAA,IAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EAAA,SAAA,CAAA,IAAA,IAAA,CAAA;QADU,KAAI,CAAA,IAAA,GAAG,yBAAyB,CAAC;;KAC3C;IAAD,OAAC,uBAAA,CAAA;AAAD,CAFA,CAA6C,KAAK,CAEjD,EAAA;AAED;;AAEG;AACI,IAAM,YAAY,GAAG,UAAU,GAAW,EAAA;AAC/C,IAAA,IAAM,SAAS,GAAGA,mBAAiB,CAAC,GAAG,CAAC,CAAC;IACzC,OAAO,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACjD,EAAE;AAEF;;;AAGG;AACI,IAAM,6BAA6B,GAAG,UAAU,GAAW,EAAA;;IAEhE,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC9C,EAAE;AAEF;;;;;;;;AAQG;AACI,IAAM,YAAY,GAAG,UAAU,GAAW,EAAA;IAC/C,IAAI;QACF,OAAO,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACvC,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAC;AAC3C,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;ACvXA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACG,SAAU,QAAQ,CAAI,KAAQ,EAAA;AAClC,IAAA,OAAO,UAAU,CAAC,SAAS,EAAE,KAAK,CAAM,CAAC;AAC3C,CAAC;AAED;;;;;;;;;;;;;AAaG;AACa,SAAA,UAAU,CAAC,MAAe,EAAE,MAAe,EAAA;AACzD,IAAA,IAAI,EAAE,MAAM,YAAY,MAAM,CAAC,EAAE;AAC/B,QAAA,OAAO,MAAM,CAAC;AACf,KAAA;IAED,QAAQ,MAAM,CAAC,WAAW;AACxB,QAAA,KAAK,IAAI;;;YAGP,IAAM,SAAS,GAAG,MAAc,CAAC;YACjC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;AAEvC,QAAA,KAAK,MAAM;YACT,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,MAAM,GAAG,EAAE,CAAC;AACb,aAAA;YACD,MAAM;AACR,QAAA,KAAK,KAAK;;YAER,MAAM,GAAG,EAAE,CAAC;YACZ,MAAM;AAER,QAAA;;AAEE,YAAA,OAAO,MAAM,CAAC;AACjB,KAAA;AAED,IAAA,KAAK,IAAM,IAAI,IAAI,MAAM,EAAE;;AAEzB,QAAA,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACrD,SAAS;AACV,SAAA;AACA,QAAA,MAAkC,CAAC,IAAI,CAAC,GAAG,UAAU,CACnD,MAAkC,CAAC,IAAI,CAAC,EACxC,MAAkC,CAAC,IAAI,CAAC,CAC1C,CAAC;AACH,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAA;IAC7B,OAAO,GAAG,KAAK,WAAW,CAAC;AAC7B;;ACjFA;;;;;;;;;;;;;;;AAeG;AAEH;;;;AAIG;SACa,SAAS,GAAA;AACvB,IAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC/B,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,QAAA,OAAO,MAAM,CAAC;AACf,KAAA;AACD,IAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,QAAA,OAAO,MAAM,CAAC;AACf,KAAA;AACD,IAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACrD;;ACjCA;;;;;;;;;;;;;;;AAeG;AAwCH,IAAM,qBAAqB,GAAG,YAAA;IAC5B,OAAA,SAAS,EAAE,CAAC,qBAAqB,CAAA;AAAjC,CAAiC,CAAC;AAEpC;;;;;;;AAOG;AACH,IAAM,0BAA0B,GAAG,YAAA;IACjC,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,GAAG,KAAK,WAAW,EAAE;QACxE,OAAO;AACR,KAAA;AACD,IAAA,IAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;AAC7D,IAAA,IAAI,kBAAkB,EAAE;AACtB,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACvC,KAAA;AACH,CAAC,CAAC;AAEF,IAAM,qBAAqB,GAAG,YAAA;AAC5B,IAAA,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;QACnC,OAAO;AACR,KAAA;AACD,IAAA,IAAI,KAAK,CAAC;IACV,IAAI;QACF,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;AAChE,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;;;QAGV,OAAO;AACR,KAAA;IACD,IAAM,OAAO,GAAG,KAAK,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,OAAO,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACxC,CAAC,CAAC;AAEF;;;;;;AAMG;AACU,IAAA,WAAW,GAAG,YAAA;IACzB,IAAI;QACF,QACE,qBAAqB,EAAE;AACvB,YAAA,0BAA0B,EAAE;YAC5B,qBAAqB,EAAE,EACvB;AACH,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACV;;;;;AAKG;AACH,QAAA,OAAO,CAAC,IAAI,CAAC,sDAA+C,CAAC,CAAE,CAAC,CAAC;QACjE,OAAO;AACR,KAAA;AACH,EAAE;AAEF;;;;;AAKG;AACU,IAAA,sBAAsB,GAAG,UACpC,WAAmB,gBACI,OAAA,CAAA,EAAA,GAAA,MAAA,WAAW,EAAE,0CAAE,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,WAAW,CAAC,CAAA,GAAC;AAErE;;;;;AAKG;AACI,IAAM,iCAAiC,GAAG,UAC/C,WAAmB,EAAA;AAEnB,IAAA,IAAM,IAAI,GAAG,sBAAsB,CAAC,WAAW,CAAC,CAAC;IACjD,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IACD,IAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7C,IAAI,cAAc,IAAI,CAAC,IAAI,cAAc,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;AAC7D,QAAA,MAAM,IAAI,KAAK,CAAC,uBAAgB,IAAI,EAAA,sCAAA,CAAsC,CAAC,CAAC;AAC7E,KAAA;;AAED,IAAA,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9D,IAAA,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;AAEnB,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACtD,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC,CAAC;AAClD,KAAA;AACH,EAAE;AAEF;;;AAGG;AACI,IAAM,mBAAmB,GAAG,YACjC,EAAA,IAAA,EAAA,CAAA,CAAA,OAAA,CAAA,EAAA,GAAA,WAAW,EAAE,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAA,GAAC;AAExB;;;;AAIG;AACU,IAAA,sBAAsB,GAAG,UACpC,IAAO,YAEP,OAAA,CAAA,EAAA,GAAA,WAAW,EAAE,0CAAG,GAAI,CAAA,MAAA,CAAA,IAAI,CAAE,CAA8B,CAAA;;AC1K1D;;;;;;;;;;;;;;;AAeG;AAEH,IAAA,QAAA,kBAAA,YAAA;AAIE,IAAA,SAAA,QAAA,GAAA;QAAA,IAKC,KAAA,GAAA,IAAA,CAAA;QAPD,IAAM,CAAA,MAAA,GAA8B,YAAO,GAAC,CAAC;QAC7C,IAAO,CAAA,OAAA,GAA8B,YAAO,GAAC,CAAC;QAE5C,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACzC,YAAA,KAAI,CAAC,OAAO,GAAG,OAAoC,CAAC;AACpD,YAAA,KAAI,CAAC,MAAM,GAAG,MAAmC,CAAC;AACpD,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;IACH,QAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UACE,QAAqD,EAAA;QADvD,IAuBC,KAAA,GAAA,IAAA,CAAA;QApBC,OAAO,UAAC,KAAK,EAAE,KAAM,EAAA;AACnB,YAAA,IAAI,KAAK,EAAE;AACT,gBAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpB,aAAA;AAAM,iBAAA;AACL,gBAAA,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACrB,aAAA;AACD,YAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;;;gBAGlC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,YAAO,GAAC,CAAC,CAAC;;;AAI7B,gBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzB,QAAQ,CAAC,KAAK,CAAC,CAAC;AACjB,iBAAA;AAAM,qBAAA;AACL,oBAAA,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxB,iBAAA;AACF,aAAA;AACH,SAAC,CAAC;KACH,CAAA;IACH,OAAC,QAAA,CAAA;AAAD,CAAC,EAAA;;ACzDD;;;;;;;;;;;;;;;AAeG;AA+Ea,SAAA,mBAAmB,CACjC,KAA+B,EAC/B,SAAkB,EAAA;IAElB,IAAI,KAAK,CAAC,GAAG,EAAE;AACb,QAAA,MAAM,IAAI,KAAK,CACb,8GAA8G,CAC/G,CAAC;AACH,KAAA;;AAED,IAAA,IAAM,MAAM,GAAG;AACb,QAAA,GAAG,EAAE,MAAM;AACX,QAAA,IAAI,EAAE,KAAK;KACZ,CAAC;AAEF,IAAA,IAAM,OAAO,GAAG,SAAS,IAAI,cAAc,CAAC;AAC5C,IAAA,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;IAC3B,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC;IACvC,IAAI,CAAC,GAAG,EAAE;AACR,QAAA,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;AACzE,KAAA;AAED,IAAA,IAAM,OAAO,GAAA,QAAA,CAAA;;AAEX,QAAA,GAAG,EAAE,iCAAA,CAAA,MAAA,CAAkC,OAAO,CAAE,EAChD,GAAG,EAAE,OAAO,EACZ,GAAG,EAAA,GAAA,EACH,GAAG,EAAE,GAAG,GAAG,IAAI,EACf,SAAS,EAAE,GAAG,EACd,GAAG,EAAA,GAAA,EACH,OAAO,EAAE,GAAG,EACZ,QAAQ,EAAE;AACR,YAAA,gBAAgB,EAAE,QAAQ;AAC1B,YAAA,UAAU,EAAE,EAAE;SACf,EAGE,EAAA,KAAK,CACT,CAAC;;IAGF,IAAM,SAAS,GAAG,EAAE,CAAC;IACrB,OAAO;AACL,QAAA,6BAA6B,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACrD,QAAA,6BAA6B,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACtD,SAAS;AACV,KAAA,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACd;;AC7IA;;;;;;;;;;;;;;;AAeG;AAKH;;;AAGG;SACa,KAAK,GAAA;IACnB,IACE,OAAO,SAAS,KAAK,WAAW;AAChC,QAAA,OAAO,SAAS,CAAC,WAAW,CAAC,KAAK,QAAQ,EAC1C;AACA,QAAA,OAAO,SAAS,CAAC,WAAW,CAAC,CAAC;AAC/B,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;AACH,CAAC;AAED;;;;;;AAMG;SACa,eAAe,GAAA;AAC7B,IAAA,QACE,OAAO,MAAM,KAAK,WAAW;;;AAG7B,QAAA,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC;AACjE,QAAA,mDAAmD,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EACjE;AACJ,CAAC;AAED;;;;AAIG;AACH;SACgB,MAAM,GAAA;;AACpB,IAAA,IAAM,gBAAgB,GAAG,CAAA,EAAA,GAAA,WAAW,EAAE,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,gBAAgB,CAAC;IACzD,IAAI,gBAAgB,KAAK,MAAM,EAAE;AAC/B,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;SAAM,IAAI,gBAAgB,KAAK,SAAS,EAAE;AACzC,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;IAED,IAAI;AACF,QAAA,QACE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,kBAAkB,EACrE;AACH,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AACH,CAAC;AAED;;AAEG;SACa,SAAS,GAAA;IACvB,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AACxD,CAAC;SAUe,kBAAkB,GAAA;AAChC,IAAA,IAAM,OAAO,GACX,OAAO,MAAM,KAAK,QAAQ;UACtB,MAAM,CAAC,OAAO;AAChB,UAAE,OAAO,OAAO,KAAK,QAAQ;cAC3B,OAAO,CAAC,OAAO;cACf,SAAS,CAAC;IAChB,OAAO,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,EAAE,KAAK,SAAS,CAAC;AACjE,CAAC;AAED;;;;AAIG;SACa,aAAa,GAAA;AAC3B,IAAA,QACE,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,SAAS,CAAC,KAAK,aAAa,EACvE;AACJ,CAAC;AAED;SACgB,UAAU,GAAA;IACxB,OAAO,KAAK,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3C,CAAC;AAED;SACgB,IAAI,GAAA;AAClB,IAAA,IAAM,EAAE,GAAG,KAAK,EAAE,CAAC;AACnB,IAAA,OAAO,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACjE,CAAC;AAED;SACgB,KAAK,GAAA;IACnB,OAAO,KAAK,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED;;;;AAIG;SACa,SAAS,GAAA;IACvB,OAAO,SAAS,CAAC,WAAW,KAAK,IAAI,IAAI,SAAS,CAAC,UAAU,KAAK,IAAI,CAAC;AACzE,CAAC;AAED;SACgB,QAAQ,GAAA;IACtB,QACE,CAAC,MAAM,EAAE;AACT,QAAA,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;QACtC,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACvC;AACJ,CAAC;AAED;;;AAGG;SACa,oBAAoB,GAAA;IAClC,IAAI;AACF,QAAA,OAAO,OAAO,SAAS,KAAK,QAAQ,CAAC;AACtC,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AACH,CAAC;AAED;;;;;;AAMG;SACa,yBAAyB,GAAA;AACvC,IAAA,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;QACjC,IAAI;YACF,IAAI,UAAQ,GAAY,IAAI,CAAC;YAC7B,IAAM,eAAa,GACjB,yDAAyD,CAAC;YAC5D,IAAM,SAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAa,CAAC,CAAC;YACnD,SAAO,CAAC,SAAS,GAAG,YAAA;AAClB,gBAAA,SAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;;gBAEvB,IAAI,CAAC,UAAQ,EAAE;AACb,oBAAA,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,eAAa,CAAC,CAAC;AAC9C,iBAAA;gBACD,OAAO,CAAC,IAAI,CAAC,CAAC;AAChB,aAAC,CAAC;YACF,SAAO,CAAC,eAAe,GAAG,YAAA;gBACxB,UAAQ,GAAG,KAAK,CAAC;AACnB,aAAC,CAAC;YAEF,SAAO,CAAC,OAAO,GAAG,YAAA;;gBAChB,MAAM,CAAC,CAAA,CAAA,EAAA,GAAA,SAAO,CAAC,KAAK,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,KAAI,EAAE,CAAC,CAAC;AACvC,aAAC,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;AACf,SAAA;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;AAIG;SACa,iBAAiB,GAAA;IAC/B,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;AAChE,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AC1MA;;;;;;;;;;;;;;;AAeG;AA8CH,IAAM,UAAU,GAAG,eAAe,CAAC;AAUnC;AACA;AACA,IAAA,aAAA,kBAAA,UAAA,MAAA,EAAA;IAAmC,SAAK,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;AAItC,IAAA,SAAA,aAAA;;AAEW,IAAA,IAAY,EACrB,OAAe;;IAER,UAAoC,EAAA;QAL7C,IAOE,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,OAAO,CAAC,IAWf,IAAA,CAAA;QAhBU,KAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QAGd,KAAU,CAAA,UAAA,GAAV,UAAU,CAA0B;;QAPpC,KAAI,CAAA,IAAA,GAAW,UAAU,CAAC;;;QAajC,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;;;QAIrD,IAAI,KAAK,CAAC,iBAAiB,EAAE;YAC3B,KAAK,CAAC,iBAAiB,CAAC,KAAI,EAAE,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC9D,SAAA;;KACF;IACH,OAAC,aAAA,CAAA;AAAD,CAvBA,CAAmC,KAAK,CAuBvC,EAAA;AAED,IAAA,YAAA,kBAAA,YAAA;AAIE,IAAA,SAAA,YAAA,CACmB,OAAe,EACf,WAAmB,EACnB,MAA2B,EAAA;QAF3B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QACnB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAqB;KAC1C;IAEJ,YAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UACE,IAAO,EAAA;QACP,IAA4D,IAAA,GAAA,EAAA,CAAA;aAA5D,IAA4D,EAAA,GAAA,CAAA,EAA5D,EAA4D,GAAA,SAAA,CAAA,MAAA,EAA5D,EAA4D,EAAA,EAAA;YAA5D,IAA4D,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;QAE5D,IAAM,UAAU,GAAI,IAAI,CAAC,CAAC,CAAe,IAAI,EAAE,CAAC;QAChD,IAAM,QAAQ,GAAG,EAAG,CAAA,MAAA,CAAA,IAAI,CAAC,OAAO,EAAA,GAAA,CAAA,CAAA,MAAA,CAAI,IAAI,CAAE,CAAC;QAC3C,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEnC,QAAA,IAAM,OAAO,GAAG,QAAQ,GAAG,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,OAAO,CAAC;;QAE3E,IAAM,WAAW,GAAG,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,WAAW,EAAA,IAAA,CAAA,CAAA,MAAA,CAAK,OAAO,EAAA,IAAA,CAAA,CAAA,MAAA,CAAK,QAAQ,EAAA,IAAA,CAAI,CAAC;QAErE,IAAM,KAAK,GAAG,IAAI,aAAa,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;AAEnE,QAAA,OAAO,KAAK,CAAC;KACd,CAAA;IACH,OAAC,YAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,SAAS,eAAe,CAAC,QAAgB,EAAE,IAAe,EAAA;IACxD,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,UAAC,CAAC,EAAE,GAAG,EAAA;AACtC,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,QAAA,OAAO,KAAK,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAI,CAAA,MAAA,CAAA,GAAG,OAAI,CAAC;AACrD,KAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAM,OAAO,GAAG,eAAe;;ACrI/B;;;;;;;;;;;;;;;AAeG;AAEH;;;;;AAKG;AACG,SAAU,QAAQ,CAAC,GAAW,EAAA;AAClC,IAAA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED;;;;AAIG;AACG,SAAU,SAAS,CAAC,IAAa,EAAA;AACrC,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9B;;AClCA;;;;;;;;;;;;;;;AAeG;AAgBH;;;;;;AAMG;AACI,IAAM,MAAM,GAAG,UAAU,KAAa,EAAA;AAC3C,IAAA,IAAI,MAAM,GAAG,EAAE,EACb,MAAM,GAAW,EAAE,EACnB,IAAI,GAAG,EAAE,EACT,SAAS,GAAG,EAAE,CAAC;IAEjB,IAAI;QACF,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/B,QAAA,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAW,CAAC;AAC1D,QAAA,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAW,CAAC;AAC1D,QAAA,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACrB,QAAA,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;AACzB,QAAA,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;AACpB,KAAA;IAAC,OAAO,CAAC,EAAE,GAAE;IAEd,OAAO;AACL,QAAA,MAAM,EAAA,MAAA;AACN,QAAA,MAAM,EAAA,MAAA;AACN,QAAA,IAAI,EAAA,IAAA;AACJ,QAAA,SAAS,EAAA,SAAA;KACV,CAAC;AACJ,EAAE;AASF;;;;;;;AAOG;AACI,IAAM,gBAAgB,GAAG,UAAU,KAAa,EAAA;IACrD,IAAM,MAAM,GAAW,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC5C,IAAA,IAAM,GAAG,GAAW,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AAC5D,IAAA,IAAI,UAAU,GAAW,CAAC,EACxB,UAAU,GAAW,CAAC,CAAC;AAEzB,IAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,QAAA,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AAChC,YAAA,UAAU,GAAG,MAAM,CAAC,KAAK,CAAW,CAAC;AACtC,SAAA;AAAM,aAAA,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AACvC,YAAA,UAAU,GAAG,MAAM,CAAC,KAAK,CAAW,CAAC;AACtC,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AAChC,YAAA,UAAU,GAAG,MAAM,CAAC,KAAK,CAAW,CAAC;AACtC,SAAA;AAAM,aAAA;;AAEL,YAAA,UAAU,GAAG,UAAU,GAAG,KAAK,CAAC;AACjC,SAAA;AACF,KAAA;IAED,QACE,CAAC,CAAC,GAAG;AACL,QAAA,CAAC,CAAC,UAAU;AACZ,QAAA,CAAC,CAAC,UAAU;AACZ,QAAA,GAAG,IAAI,UAAU;QACjB,GAAG,IAAI,UAAU,EACjB;AACJ,EAAE;AAEF;;;;;;AAMG;AACI,IAAM,YAAY,GAAG,UAAU,KAAa,EAAA;IACjD,IAAM,MAAM,GAAW,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC5C,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;AAC9D,QAAA,OAAO,MAAM,CAAC,KAAK,CAAW,CAAC;AAChC,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd,EAAE;AAEF;;;;;;AAMG;AACI,IAAM,aAAa,GAAG,UAAU,KAAa,EAAA;AAClD,IAAA,IAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,EAC3B,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAE1B,IAAA,OAAO,CAAC,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAChF,EAAE;AAEF;;;;;;AAMG;AACI,IAAM,OAAO,GAAG,UAAU,KAAa,EAAA;IAC5C,IAAM,MAAM,GAAW,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC5C,OAAO,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC;AAChE;;ACjJA;;;;;;;;;;;;;;;AAeG;AAEa,SAAA,QAAQ,CAAmB,GAAM,EAAE,GAAW,EAAA;AAC5D,IAAA,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxD,CAAC;AAEe,SAAA,OAAO,CACrB,GAAM,EACN,GAAM,EAAA;AAEN,IAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;AAClD,QAAA,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;AACjB,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;AACH,CAAC;AAEK,SAAU,OAAO,CAAC,GAAW,EAAA;AACjC,IAAA,KAAK,IAAM,GAAG,IAAI,GAAG,EAAE;AACrB,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;AAClD,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;SAEe,GAAG,CACjB,GAAsB,EACtB,EAAmD,EACnD,UAAoB,EAAA;IAEpB,IAAM,GAAG,GAA+B,EAAE,CAAC;AAC3C,IAAA,KAAK,IAAM,GAAG,IAAI,GAAG,EAAE;AACrB,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;AAClD,YAAA,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpD,SAAA;AACF,KAAA;AACD,IAAA,OAAO,GAAwB,CAAC;AAClC,CAAC;AAED;;AAEG;AACa,SAAA,SAAS,CAAC,CAAS,EAAE,CAAS,EAAA;IAC5C,IAAI,CAAC,KAAK,CAAC,EAAE;AACX,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IAED,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAA,KAAgB,UAAK,EAAL,OAAA,GAAA,KAAK,EAAL,EAAK,GAAA,OAAA,CAAA,MAAA,EAAL,IAAK,EAAE;AAAlB,QAAA,IAAM,CAAC,GAAA,OAAA,CAAA,EAAA,CAAA,CAAA;AACV,QAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AACtB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAED,QAAA,IAAM,KAAK,GAAI,CAA6B,CAAC,CAAC,CAAC,CAAC;AAChD,QAAA,IAAM,KAAK,GAAI,CAA6B,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACtC,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;AAC5B,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AACF,SAAA;aAAM,IAAI,KAAK,KAAK,KAAK,EAAE;AAC1B,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,KAAgB,UAAK,EAAL,OAAA,GAAA,KAAK,EAAL,EAAK,GAAA,OAAA,CAAA,MAAA,EAAL,IAAK,EAAE;AAAlB,QAAA,IAAM,CAAC,GAAA,OAAA,CAAA,EAAA,CAAA,CAAA;AACV,QAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AACtB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,QAAQ,CAAC,KAAc,EAAA;IAC9B,OAAO,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;AACrD;;AC3FA;;;;;;;;;;;;;;;AAeG;AAIH;;;AAGG;AACa,SAAA,kBAAkB,CAChC,OAAmB,EACnB,QAAe,EAAA;AAAf,IAAA,IAAA,QAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAe,GAAA,IAAA,CAAA,EAAA;AAEf,IAAA,IAAM,eAAe,GAAG,IAAI,QAAQ,EAAK,CAAC;AAC1C,IAAA,UAAU,CAAC,YAAA,EAAM,OAAA,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAlC,EAAkC,EAAE,QAAQ,CAAC,CAAC;IAC/D,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;IAC9D,OAAO,eAAe,CAAC,OAAO,CAAC;AACjC;;AC/BA;;;;;;;;;;;;;;;AAeG;AAEH;;;;AAIG;AACG,SAAU,WAAW,CAAC,iBAE3B,EAAA;IACC,IAAM,MAAM,GAAG,EAAE,CAAC;AACN,IAAA,IAAA,OAAA,GAAA,UAAA,GAAG,EAAE,KAAK,EAAA;AACpB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,YAAA,KAAK,CAAC,OAAO,CAAC,UAAA,QAAQ,EAAA;AACpB,gBAAA,MAAM,CAAC,IAAI,CACT,kBAAkB,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAC7D,CAAC;AACJ,aAAC,CAAC,CAAC;AACJ,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;AACxE,SAAA;;AATH,IAAA,KAA2B,IAAiC,EAAA,GAAA,CAAA,EAAjC,EAAA,GAAA,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAjC,EAAiC,GAAA,EAAA,CAAA,MAAA,EAAjC,EAAiC,EAAA,EAAA;AAAjD,QAAA,IAAA,WAAY,EAAX,GAAG,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,KAAK,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAAV,QAAA,OAAA,CAAA,GAAG,EAAE,KAAK,CAAA,CAAA;AAUrB,KAAA;AACD,IAAA,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AACrD,CAAC;AAED;;;AAGG;AACG,SAAU,iBAAiB,CAAC,WAAmB,EAAA;IACnD,IAAM,GAAG,GAA2B,EAAE,CAAC;AACvC,IAAA,IAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAEzD,IAAA,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK,EAAA;AAClB,QAAA,IAAI,KAAK,EAAE;AACH,YAAA,IAAA,EAAe,GAAA,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAA9B,GAAG,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,KAAK,QAAoB,CAAC;YACtC,GAAG,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAC1D,SAAA;AACH,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;AAEG;AACG,SAAU,kBAAkB,CAAC,GAAW,EAAA;IAC5C,IAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC,UAAU,EAAE;AACf,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;IACD,IAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;AACnD,IAAA,OAAO,GAAG,CAAC,SAAS,CAClB,UAAU,EACV,aAAa,GAAG,CAAC,GAAG,aAAa,GAAG,SAAS,CAC9C,CAAC;AACJ;;ACtEA;;;;;;;;;;;;;;;AAeG;AAEH;;;;;;;;;;;;;;AAcG;AAEH;;;;;;;AAOG;AACH,IAAA,IAAA,kBAAA,YAAA;AAuCE,IAAA,SAAA,IAAA,GAAA;AAtCA;;;;AAIG;QACK,IAAM,CAAA,MAAA,GAAa,EAAE,CAAC;AAE9B;;;AAGG;QACK,IAAI,CAAA,IAAA,GAAa,EAAE,CAAC;AAE5B;;;;AAIG;QACK,IAAE,CAAA,EAAA,GAAa,EAAE,CAAC;AAE1B;;;AAGG;QACK,IAAI,CAAA,IAAA,GAAa,EAAE,CAAC;AAE5B;;AAEG;QACK,IAAM,CAAA,MAAA,GAAW,CAAC,CAAC;AAE3B;;AAEG;QACK,IAAM,CAAA,MAAA,GAAW,CAAC,CAAC;AAKzB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC;AAEzB,QAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE;AACvC,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAClB,SAAA;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;KACd;AAED,IAAA,IAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AAC5B,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AAC5B,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AAC5B,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AAC5B,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AAE5B,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAChB,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;KACjB,CAAA;AAED;;;;;AAKG;AACH,IAAA,IAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,GAAmC,EAAE,MAAe,EAAA;QAC5D,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,GAAG,CAAC,CAAC;AACZ,SAAA;AAED,QAAA,IAAM,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;;AAGlB,QAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;;;;;;;;;gBAS3B,CAAC,CAAC,CAAC,CAAC;oBACF,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE;yBAC5B,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;yBACjC,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AACjC,wBAAA,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7B,MAAM,IAAI,CAAC,CAAC;AACb,aAAA;AACF,SAAA;AAAM,aAAA;YACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC3B,CAAC,CAAC,CAAC,CAAC;AACF,oBAAA,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;yBACjB,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;yBACtB,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AACtB,wBAAA,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAClB,MAAM,IAAI,CAAC,CAAC;AACb,aAAA;AACF,SAAA;;QAGD,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AAC5B,YAAA,IAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACtD,YAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,UAAU,CAAC;AAC7C,SAAA;QAED,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,EAAE,CAAC,CAAC;;QAGT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,GAAG,EAAE,EAAE;gBACV,IAAI,CAAC,GAAG,EAAE,EAAE;AACV,oBAAA,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,GAAG,UAAU,CAAC;AAChB,iBAAA;AAAM,qBAAA;AACL,oBAAA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBACd,CAAC,GAAG,UAAU,CAAC;AAChB,iBAAA;AACF,aAAA;AAAM,iBAAA;gBACL,IAAI,CAAC,GAAG,EAAE,EAAE;AACV,oBAAA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC,GAAG,UAAU,CAAC;AAChB,iBAAA;AAAM,qBAAA;AACL,oBAAA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBACd,CAAC,GAAG,UAAU,CAAC;AAChB,iBAAA;AACF,aAAA;AAED,YAAA,IAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC;YACpE,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;AACN,YAAA,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,UAAU,CAAC;YACzC,CAAC,GAAG,CAAC,CAAC;YACN,CAAC,GAAG,CAAC,CAAC;AACP,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC;AACnD,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC;AACnD,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC;AACnD,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC;AACnD,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC;KACpD,CAAA;AAED,IAAA,IAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UAAO,KAAsC,EAAE,MAAe,EAAA;;QAE5D,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO;AACR,SAAA;QAED,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,YAAA,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AACvB,SAAA;AAED,QAAA,IAAM,gBAAgB,GAAG,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,CAAC;;AAEV,QAAA,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AACtB,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;;QAGxB,OAAO,CAAC,GAAG,MAAM,EAAE;;;;;YAKjB,IAAI,KAAK,KAAK,CAAC,EAAE;gBACf,OAAO,CAAC,IAAI,gBAAgB,EAAE;AAC5B,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACzB,oBAAA,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC;AACrB,iBAAA;AACF,aAAA;AAED,YAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,OAAO,CAAC,GAAG,MAAM,EAAE;oBACjB,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACjC,oBAAA,EAAE,KAAK,CAAC;AACR,oBAAA,EAAE,CAAC,CAAC;AACJ,oBAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,wBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBACpB,KAAK,GAAG,CAAC,CAAC;;wBAEV,MAAM;AACP,qBAAA;AACF,iBAAA;AACF,aAAA;AAAM,iBAAA;gBACL,OAAO,CAAC,GAAG,MAAM,EAAE;oBACjB,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,oBAAA,EAAE,KAAK,CAAC;AACR,oBAAA,EAAE,CAAC,CAAC;AACJ,oBAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,wBAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBACpB,KAAK,GAAG,CAAC,CAAC;;wBAEV,MAAM;AACP,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC;KACvB,CAAA;;AAGD,IAAA,IAAA,CAAA,SAAA,CAAA,MAAM,GAAN,YAAA;QACE,IAAM,MAAM,GAAa,EAAE,CAAC;AAC5B,QAAA,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;;AAGhC,QAAA,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE;AACpB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1C,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AAC7D,SAAA;;AAGD,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,GAAG,CAAC;AAC/B,YAAA,SAAS,IAAI,GAAG,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1B,YAAA,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AAC/B,gBAAA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;AACxC,gBAAA,EAAE,CAAC,CAAC;AACL,aAAA;AACF,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf,CAAA;IACH,OAAC,IAAA,CAAA;AAAD,CAAC,EAAA;;ACrOD;;;;;;;AAOG;AACa,SAAA,eAAe,CAC7B,QAAqB,EACrB,aAA2B,EAAA;IAE3B,IAAM,KAAK,GAAG,IAAI,aAAa,CAAI,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC5D,OAAO,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrC,CAAC;AAED;;;AAGG;AACH,IAAA,aAAA,kBAAA,YAAA;AAUE;;;;AAIG;IACH,SAAY,aAAA,CAAA,QAAqB,EAAE,aAA2B,EAAA;QAA9D,IAYC,KAAA,GAAA,IAAA,CAAA;QA1BO,IAAS,CAAA,SAAA,GAAmC,EAAE,CAAC;QAC/C,IAAY,CAAA,YAAA,GAAkB,EAAE,CAAC;QAEjC,IAAa,CAAA,aAAA,GAAG,CAAC,CAAC;;AAElB,QAAA,IAAA,CAAA,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACzB,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;AASxB,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;;;;AAInC,QAAA,IAAI,CAAC,IAAI;AACN,aAAA,IAAI,CAAC,YAAA;YACJ,QAAQ,CAAC,KAAI,CAAC,CAAC;AACjB,SAAC,CAAC;aACD,KAAK,CAAC,UAAA,CAAC,EAAA;AACN,YAAA,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChB,SAAC,CAAC,CAAC;KACN;IAED,aAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,KAAQ,EAAA;AACX,QAAA,IAAI,CAAC,eAAe,CAAC,UAAC,QAAqB,EAAA;AACzC,YAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvB,SAAC,CAAC,CAAC;KACJ,CAAA;IAED,aAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,KAAY,EAAA;AAChB,QAAA,IAAI,CAAC,eAAe,CAAC,UAAC,QAAqB,EAAA;AACzC,YAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACxB,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACnB,CAAA;AAED,IAAA,aAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,YAAA;AACE,QAAA,IAAI,CAAC,eAAe,CAAC,UAAC,QAAqB,EAAA;YACzC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACtB,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,EAAE,CAAC;KACd,CAAA;AAED;;;;;AAKG;AACH,IAAA,aAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UACE,cAA+C,EAC/C,KAAe,EACf,QAAqB,EAAA;QAHvB,IAkEC,KAAA,GAAA,IAAA,CAAA;AA7DC,QAAA,IAAI,QAAqB,CAAC;QAE1B,IACE,cAAc,KAAK,SAAS;AAC5B,YAAA,KAAK,KAAK,SAAS;YACnB,QAAQ,KAAK,SAAS,EACtB;AACA,YAAA,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;AACtC,SAAA;;QAGD,IACE,oBAAoB,CAAC,cAA4C,EAAE;YACjE,MAAM;YACN,OAAO;YACP,UAAU;AACX,SAAA,CAAC,EACF;YACA,QAAQ,GAAG,cAA6B,CAAC;AAC1C,SAAA;AAAM,aAAA;AACL,YAAA,QAAQ,GAAG;AACT,gBAAA,IAAI,EAAE,cAA2B;AACjC,gBAAA,KAAK,EAAA,KAAA;AACL,gBAAA,QAAQ,EAAA,QAAA;aACM,CAAC;AAClB,SAAA;AAED,QAAA,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;AAC/B,YAAA,QAAQ,CAAC,IAAI,GAAG,IAAiB,CAAC;AACnC,SAAA;AACD,QAAA,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,EAAE;AAChC,YAAA,QAAQ,CAAC,KAAK,GAAG,IAAe,CAAC;AAClC,SAAA;AACD,QAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE;AACnC,YAAA,QAAQ,CAAC,QAAQ,GAAG,IAAkB,CAAC;AACxC,SAAA;AAED,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAU,CAAC,MAAM,CAAC,CAAC;;;;QAKrE,IAAI,IAAI,CAAC,SAAS,EAAE;;AAElB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAA;gBACb,IAAI;oBACF,IAAI,KAAI,CAAC,UAAU,EAAE;AACnB,wBAAA,QAAQ,CAAC,KAAK,CAAC,KAAI,CAAC,UAAU,CAAC,CAAC;AACjC,qBAAA;AAAM,yBAAA;wBACL,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACrB,qBAAA;AACF,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;;AAEX,iBAAA;gBACD,OAAO;AACT,aAAC,CAAC,CAAC;AACJ,SAAA;AAED,QAAA,IAAI,CAAC,SAAU,CAAC,IAAI,CAAC,QAAuB,CAAC,CAAC;AAE9C,QAAA,OAAO,KAAK,CAAC;KACd,CAAA;;;IAIO,aAAc,CAAA,SAAA,CAAA,cAAA,GAAtB,UAAuB,CAAS,EAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YACnE,OAAO;AACR,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAEzB,QAAA,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC;QACxB,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AAChE,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC1B,SAAA;KACF,CAAA;IAEO,aAAe,CAAA,SAAA,CAAA,eAAA,GAAvB,UAAwB,EAAmC,EAAA;QACzD,IAAI,IAAI,CAAC,SAAS,EAAE;;YAElB,OAAO;AACR,SAAA;;;AAID,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,YAAA,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACrB,SAAA;KACF,CAAA;;;;AAKO,IAAA,aAAA,CAAA,SAAA,CAAA,OAAO,GAAf,UAAgB,CAAS,EAAE,EAAmC,EAAA;QAA9D,IAiBC,KAAA,GAAA,IAAA,CAAA;;;AAdC,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAA;AACb,YAAA,IAAI,KAAI,CAAC,SAAS,KAAK,SAAS,IAAI,KAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBACnE,IAAI;oBACF,EAAE,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;;;;oBAIV,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,KAAK,EAAE;AACnD,wBAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,qBAAA;AACF,iBAAA;AACF,aAAA;AACH,SAAC,CAAC,CAAC;KACJ,CAAA;IAEO,aAAK,CAAA,SAAA,CAAA,KAAA,GAAb,UAAc,GAAW,EAAA;QAAzB,IAcC,KAAA,GAAA,IAAA,CAAA;QAbC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;AACR,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,GAAG,KAAK,SAAS,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;AACvB,SAAA;;;AAGD,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAA;AACb,YAAA,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,YAAA,KAAI,CAAC,aAAa,GAAG,SAAS,CAAC;AACjC,SAAC,CAAC,CAAC;KACJ,CAAA;IACH,OAAC,aAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED;AACA;AACgB,SAAA,KAAK,CAAC,EAAY,EAAE,OAAiB,EAAA;IACnD,OAAO,YAAA;QAAC,IAAkB,IAAA,GAAA,EAAA,CAAA;aAAlB,IAAkB,EAAA,GAAA,CAAA,EAAlB,EAAkB,GAAA,SAAA,CAAA,MAAA,EAAlB,EAAkB,EAAA,EAAA;YAAlB,IAAkB,CAAA,EAAA,CAAA,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;;AACxB,QAAA,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;AAClB,aAAA,IAAI,CAAC,YAAA;YACJ,EAAE,CAAA,KAAA,CAAA,KAAA,CAAA,EAAI,IAAI,CAAE,CAAA;AACd,SAAC,CAAC;aACD,KAAK,CAAC,UAAC,KAAY,EAAA;AAClB,YAAA,IAAI,OAAO,EAAE;gBACX,OAAO,CAAC,KAAK,CAAC,CAAC;AAChB,aAAA;AACH,SAAC,CAAC,CAAC;AACP,KAAC,CAAC;AACJ,CAAC;AAED;;AAEG;AACH,SAAS,oBAAoB,CAC3B,GAA+B,EAC/B,OAAiB,EAAA;IAEjB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;AAC3C,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,KAAqB,UAAO,EAAP,SAAA,GAAA,OAAO,EAAP,EAAO,GAAA,SAAA,CAAA,MAAA,EAAP,IAAO,EAAE;AAAzB,QAAA,IAAM,MAAM,GAAA,SAAA,CAAA,EAAA,CAAA,CAAA;QACf,IAAI,MAAM,IAAI,GAAG,IAAI,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE;AACtD,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACF,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,IAAI,GAAA;;AAEb;;AC5SA;;;;;;;;;;;;;;;AAeG;AAEH;;;;;;;;AAQG;AACU,IAAA,gBAAgB,GAAG,UAC9B,MAAc,EACd,QAAgB,EAChB,QAAgB,EAChB,QAAgB,EAAA;AAEhB,IAAA,IAAI,QAAQ,CAAC;IACb,IAAI,QAAQ,GAAG,QAAQ,EAAE;AACvB,QAAA,QAAQ,GAAG,WAAW,GAAG,QAAQ,CAAC;AACnC,KAAA;SAAM,IAAI,QAAQ,GAAG,QAAQ,EAAE;AAC9B,QAAA,QAAQ,GAAG,QAAQ,KAAK,CAAC,GAAG,MAAM,GAAG,eAAe,GAAG,QAAQ,CAAC;AACjE,KAAA;AACD,IAAA,IAAI,QAAQ,EAAE;QACZ,IAAM,KAAK,GACT,MAAM;YACN,2BAA2B;YAC3B,QAAQ;aACP,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,aAAa,CAAC;YAC/C,WAAW;YACX,QAAQ;AACR,YAAA,GAAG,CAAC;AACN,QAAA,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;AACxB,KAAA;AACH,EAAE;AAEF;;;;;;AAMG;AACa,SAAA,WAAW,CAAC,MAAc,EAAE,OAAe,EAAA;AACzD,IAAA,OAAO,EAAG,CAAA,MAAA,CAAA,MAAM,EAAY,WAAA,CAAA,CAAA,MAAA,CAAA,OAAO,eAAY,CAAC;AAClD,CAAC;AAED;;;;;AAKG;SACa,iBAAiB,CAC/B,MAAc,EACd,SAAiB,EACjB,QAAiB,EAAA;AAEjB,IAAA,IAAI,QAAQ,IAAI,CAAC,SAAS,EAAE;QAC1B,OAAO;AACR,KAAA;AACD,IAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;;AAEjC,QAAA,MAAM,IAAI,KAAK,CACb,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,qCAAqC,CACzE,CAAC;AACH,KAAA;AACH,CAAC;AAEe,SAAA,gBAAgB,CAC9B,MAAc,EACd,YAAoB;AACpB;AACA,QAAkB,EAClB,QAAiB,EAAA;AAEjB,IAAA,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE;QACzB,OAAO;AACR,KAAA;AACD,IAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AAClC,QAAA,MAAM,IAAI,KAAK,CACb,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,2BAA2B,CAChE,CAAC;AACH,KAAA;AACH,CAAC;AAEK,SAAU,qBAAqB,CACnC,MAAc,EACd,YAAoB,EACpB,OAAgB,EAChB,QAAiB,EAAA;AAEjB,IAAA,IAAI,QAAQ,IAAI,CAAC,OAAO,EAAE;QACxB,OAAO;AACR,KAAA;IACD,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI,EAAE;AACnD,QAAA,MAAM,IAAI,KAAK,CACb,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,GAAG,iCAAiC,CACtE,CAAC;AACH,KAAA;AACH;;ACnHA;;;;;;;;;;;;;;;AAeG;AAIH;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAGG;AACI,IAAM,iBAAiB,GAAG,UAAU,GAAW,EAAA;IACpD,IAAM,GAAG,GAAa,EAAE,CAAC;IACzB,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;;AAG1B,QAAA,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;AAC9B,YAAA,IAAM,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC;AACxB,YAAA,CAAC,EAAE,CAAC;YACJ,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,yCAAyC,CAAC,CAAC;AAClE,YAAA,IAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;YACvC,CAAC,GAAG,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC;AAClC,SAAA;QAED,IAAI,CAAC,GAAG,GAAG,EAAE;AACX,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACd,SAAA;aAAM,IAAI,CAAC,GAAG,IAAI,EAAE;AACnB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;AAC1B,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC3B,SAAA;aAAM,IAAI,CAAC,GAAG,KAAK,EAAE;AACpB,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AAC3B,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AACjC,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC3B,SAAA;AAAM,aAAA;AACL,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AAC3B,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,CAAC;AAClC,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;AACjC,YAAA,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;AAC3B,SAAA;AACF,KAAA;AACD,IAAA,OAAO,GAAG,CAAC;AACb,EAAE;AAEF;;;;AAIG;AACI,IAAM,YAAY,GAAG,UAAU,GAAW,EAAA;IAC/C,IAAI,CAAC,GAAG,CAAC,CAAC;AACV,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAM,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,GAAG,GAAG,EAAE;AACX,YAAA,CAAC,EAAE,CAAC;AACL,SAAA;aAAM,IAAI,CAAC,GAAG,IAAI,EAAE;YACnB,CAAC,IAAI,CAAC,CAAC;AACR,SAAA;AAAM,aAAA,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,EAAE;;YAErC,CAAC,IAAI,CAAC,CAAC;YACP,CAAC,EAAE,CAAC;AACL,SAAA;AAAM,aAAA;YACL,CAAC,IAAI,CAAC,CAAC;AACR,SAAA;AACF,KAAA;AACD,IAAA,OAAO,CAAC,CAAC;AACX;;AC1FA;;;;;;;;;;;;;;;AAeG;AAEH;;;;AAIG;AACU,IAAA,MAAM,GAAG,YAAA;AACpB,IAAA,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAA,CAAC,EAAA;AAC9D,QAAA,IAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,EAChC,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;AACtC,QAAA,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AACxB,KAAC,CAAC,CAAC;AACL;;AC5BA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACH,IAAM,uBAAuB,GAAG,IAAI,CAAC;AAErC;;;AAGG;AACH,IAAM,sBAAsB,GAAG,CAAC,CAAC;AAEjC;;;;AAIG;AACI,IAAM,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK;AAEnD;;;;;;;AAOG;AACI,IAAM,aAAa,GAAG,IAAI;AAEjC;;;;AAIG;SACa,sBAAsB,CACpC,YAAoB,EACpB,cAAgD,EAChD,aAA8C,EAAA;AAD9C,IAAA,IAAA,cAAA,KAAA,KAAA,CAAA,EAAA,EAAA,cAAgD,GAAA,uBAAA,CAAA,EAAA;AAChD,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,EAAA,EAAA,aAA8C,GAAA,sBAAA,CAAA,EAAA;;;;AAK9C,IAAA,IAAM,aAAa,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;;;AAI7E,IAAA,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK;;;IAG3B,aAAa;QACX,aAAa;;;AAGb,SAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;AACrB,QAAA,CAAC,CACJ,CAAC;;IAGF,OAAO,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,aAAa,GAAG,UAAU,CAAC,CAAC;AAChE;;AC3EA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACG,SAAU,OAAO,CAAC,CAAS,EAAA;AAC/B,IAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACvB,OAAO,EAAA,CAAA,MAAA,CAAG,CAAC,CAAE,CAAC;AACf,KAAA;AACD,IAAA,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,SAAS,CAAC,CAAS,EAAA;AAC1B,IAAA,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChB,IAAA,IAAM,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AACrB,IAAA,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE;AAC5B,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,IAAM,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;IACnB,IAAI,GAAG,KAAK,CAAC,EAAE;AACb,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IACD,IAAI,GAAG,KAAK,CAAC,EAAE;AACb,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IACD,IAAI,GAAG,KAAK,CAAC,EAAE;AACb,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;AC5CA;;;;;;;;;;;;;;;AAeG;AAMG,SAAU,kBAAkB,CAChC,OAAwC,EAAA;AAExC,IAAA,IAAI,OAAO,IAAK,OAA8B,CAAC,SAAS,EAAE;QACxD,OAAQ,OAA8B,CAAC,SAAS,CAAC;AAClD,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,OAAqB,CAAC;AAC9B,KAAA;AACH;;;;"}