# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.9](https://github.com/es-shims/String.prototype.trimEnd/compare/v1.0.8...v1.0.9) - 2024-12-11

### Commits

- [actions] split out node 10-20, and 20+ [`7e5ffdc`](https://github.com/es-shims/String.prototype.trimEnd/commit/7e5ffdc2ab30b09e19773f35df5dceffddc660d4)
- [meta] sort package.json mildly [`2f99c8b`](https://github.com/es-shims/String.prototype.trimEnd/commit/2f99c8bc69d50dc0c56af3900cc31129ef42bc1b)
- [<PERSON>] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `tape` [`2774fe6`](https://github.com/es-shims/String.prototype.trimEnd/commit/2774fe6660c9de92eacf2a8173f494aa151b1fc1)
- [Refactor] use `call-bound` directly [`9e3bbec`](https://github.com/es-shims/String.prototype.trimEnd/commit/9e3bbec66695b715ec38f832aa472e1a82ffe095)
- [Tests] replace `aud` with `npm audit` [`cb9a462`](https://github.com/es-shims/String.prototype.trimEnd/commit/cb9a4623b86d2a91cb0a5b704739b6fe43078abd)
- [meta] add missing `engines.node` [`f46c829`](https://github.com/es-shims/String.prototype.trimEnd/commit/f46c829985a3c78e92482247fe30800781b4a9e3)
- [Deps] update `call-bind` [`e892c32`](https://github.com/es-shims/String.prototype.trimEnd/commit/e892c32d30aaaee372c8159b7ac94d564cf7f0e9)
- [Dev Deps] add missing peer dep [`e1a59da`](https://github.com/es-shims/String.prototype.trimEnd/commit/e1a59da39cc18bbe546371b12d39f86b4c947896)

## [v1.0.8](https://github.com/es-shims/String.prototype.trimEnd/compare/v1.0.7...v1.0.8) - 2024-03-16

### Commits

- [Refactor] replace `es-abstract` with `es-object-atoms` [`0df2b01`](https://github.com/es-shims/String.prototype.trimEnd/commit/0df2b018d3ba214e2435b9841a7a03fc06c24fd0)
- [Dev Deps] update `aud`, `npmignore`, `tape` [`190e9c5`](https://github.com/es-shims/String.prototype.trimEnd/commit/190e9c5d74f84d8f90f61c9e9ad4b05f657df830)

## [v1.0.7](https://github.com/es-shims/String.prototype.trimEnd/compare/v1.0.6...v1.0.7) - 2023-09-07

### Commits

- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`1a10293`](https://github.com/es-shims/String.prototype.trimEnd/commit/1a102935636b564cd346d83666f62a9398864081)
- [Deps] update `define-properties`, `es-abstract` [`6ba2e19`](https://github.com/es-shims/String.prototype.trimEnd/commit/6ba2e19a78f9c63bad2daf0627ce7f2e33f1aeb9)

## [v1.0.6](https://github.com/es-shims/String.prototype.trimEnd/compare/v1.0.5...v1.0.6) - 2022-11-07

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`1d1e717`](https://github.com/es-shims/String.prototype.trimEnd/commit/1d1e71720ada81e484c9bffb386f65d7a6a1335e)
- [actions] update rebase action to use reusable workflow [`83f2683`](https://github.com/es-shims/String.prototype.trimEnd/commit/83f268325cc37ca72bea995281167a9d2821810b)
- [Dev Deps] update `aud`, `tape` [`a3a9129`](https://github.com/es-shims/String.prototype.trimEnd/commit/a3a9129dd995a949bdb93d278f403aa400e088ed)
- [Deps] update `es-abstract` [`a6e476d`](https://github.com/es-shims/String.prototype.trimEnd/commit/a6e476dc0120a41a22e66b5079bf3b407da387fa)

## [v1.0.5](https://github.com/es-shims/String.prototype.trimEnd/compare/v1.0.4...v1.0.5) - 2022-05-02

### Commits

- [actions] reuse common workflows [`69a56ce`](https://github.com/es-shims/String.prototype.trimEnd/commit/69a56ce95343848d5e498f8e939a0697db8ac9f2)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`5d7db31`](https://github.com/es-shims/String.prototype.trimEnd/commit/5d7db314948faac975705db56305b6cc3dae7ba9)
- [Fix] ensure main entry point properly checks the receiver in ES3 engines [`bb1983d`](https://github.com/es-shims/String.prototype.trimEnd/commit/bb1983dc27504733ab883a42f55b134f679a642d)
- [Fix] as of unicode v6, the mongolian vowel separator is no longer whitespace [`10a1091`](https://github.com/es-shims/String.prototype.trimEnd/commit/10a10916e1ffc8cdab78515b2cc521cc74c08277)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`a08e14b`](https://github.com/es-shims/String.prototype.trimEnd/commit/a08e14bfea5e27d419ef742e83f467a169a69971)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `functions-have-names`, `tape` [`1c4c8da`](https://github.com/es-shims/String.prototype.trimEnd/commit/1c4c8dab4aefc1c5823a61b5a0286bb6fe1e036c)
- [actions] update codecov uploader [`70c4a7c`](https://github.com/es-shims/String.prototype.trimEnd/commit/70c4a7c566fd5bf72475df9318ec2cd218ceca26)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `auto-changelog`, `tape` [`4b08ed7`](https://github.com/es-shims/String.prototype.trimEnd/commit/4b08ed78a579585b593b3ca4ac7919d13906e840)
- [readme] add github actions/codecov badges [`9805501`](https://github.com/es-shims/String.prototype.trimEnd/commit/980550105b304385bcccb8ec05a247e02c24d04a)
- [Dev Deps] update `eslint`, `tape` [`50ec335`](https://github.com/es-shims/String.prototype.trimEnd/commit/50ec335ed42b59d1ab626dbe412f4ae23ab99b4b)
- [actions] update workflows [`bf9c32e`](https://github.com/es-shims/String.prototype.trimEnd/commit/bf9c32efe08e98b53302df67581dc971b7a758cd)
- [meta] use `prepublishOnly` script for npm 7+ [`9d921bd`](https://github.com/es-shims/String.prototype.trimEnd/commit/9d921bdbcaf8cebebbc302e6df518cbc3e4907bd)
- [Deps] update `define-properties` [`15617ce`](https://github.com/es-shims/String.prototype.trimEnd/commit/15617ced8ac2ea3ab8324a04d16fd294b12059b5)

## [v1.0.4](https://github.com/es-shims/String.prototype.trimEnd/compare/v1.0.3...v1.0.4) - 2021-02-23

### Commits

- [meta] do not publish github action workflow files [`08e735c`](https://github.com/es-shims/String.prototype.trimEnd/commit/08e735cd55b00ae78a9dc16c6b4e786f7931085b)
- [readme] remove travis badge [`10e0e47`](https://github.com/es-shims/String.prototype.trimEnd/commit/10e0e47cb2ecfd171e68b40f8486c5d007dcf3ef)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `functions-have-names`, `has-strict-mode`, `tape` [`0871432`](https://github.com/es-shims/String.prototype.trimEnd/commit/0871432c70c2f6a3929acd740a5d1f57c939f345)
- [Tests] increase coverage [`711e6a6`](https://github.com/es-shims/String.prototype.trimEnd/commit/711e6a66660f30f7a30fef536be435af1a13d05c)
- [actions] update workflows [`deb0d06`](https://github.com/es-shims/String.prototype.trimEnd/commit/deb0d06f41ac1c3e1e640ecd1cf0e69303ab5799)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`e250b4a`](https://github.com/es-shims/String.prototype.trimEnd/commit/e250b4a38401e7c02a067dab26cc68316da47ef7)
- [meta] gitignore coverage output [`55231df`](https://github.com/es-shims/String.prototype.trimEnd/commit/55231dfd9829277ba5c3f07be5434dc385703ca9)
- [Deps] update `call-bind` [`0580f5f`](https://github.com/es-shims/String.prototype.trimEnd/commit/0580f5f915ecb87677764d03fe3cf023e3bee7d8)

## [v1.0.3](https://github.com/es-shims/String.prototype.trimEnd/compare/v1.0.2...v1.0.3) - 2020-11-21

### Commits

- [Tests] migrate tests to Github Actions [`23e7a09`](https://github.com/es-shims/String.prototype.trimEnd/commit/23e7a09a4ad37c21c3db3d7761212c7d84a371a2)
- [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner [`26e8623`](https://github.com/es-shims/String.prototype.trimEnd/commit/26e8623cf35c1859d0b482d4bb5b3450d101a810)
- [Tests] run `nyc` on all tests [`a72a546`](https://github.com/es-shims/String.prototype.trimEnd/commit/a72a546f671c5d3ac65dff68b4db1a1cc7089bfd)
- [Deps] replace `es-abstract` with `call-bind` [`f07b87d`](https://github.com/es-shims/String.prototype.trimEnd/commit/f07b87dd452090a2601d666edceb1daa90d45f24)
- [Dev Deps] update `eslint`, `aud`; add `safe-publish-latest` [`122ecb7`](https://github.com/es-shims/String.prototype.trimEnd/commit/122ecb726b1dc043b9ef27fa5a7b4172a4d5df37)

## [v1.0.2](https://github.com/es-shims/String.prototype.trimEnd/compare/v1.0.1...v1.0.2) - 2020-10-20

### Commits

- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`a003e71`](https://github.com/es-shims/String.prototype.trimEnd/commit/a003e7166d8de16c551a14b0ec855187357cce43)
- [actions] add "Allow Edits" workflow [`0b4b43c`](https://github.com/es-shims/String.prototype.trimEnd/commit/0b4b43cb605f7b3532e61c43dfc7f1795296c5a4)
- [Deps] update `es-abstract` [`75ca6b0`](https://github.com/es-shims/String.prototype.trimEnd/commit/75ca6b0e9757d64013ae863cfaac49ebcb36f1cf)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`552016c`](https://github.com/es-shims/String.prototype.trimEnd/commit/552016cb631ac13c12bbbc0d6dd65012e5e79583)

## [v1.0.1](https://github.com/es-shims/String.prototype.trimEnd/compare/v1.0.0...v1.0.1) - 2020-04-09

### Commits

- [meta] add some missing repo metadata [`6abe248`](https://github.com/es-shims/String.prototype.trimEnd/commit/6abe248ba0b57a8b0e16bbe01de07a4d37c421bc)
- [Dev Deps] update `auto-changelog` [`e2eaab2`](https://github.com/es-shims/String.prototype.trimEnd/commit/e2eaab2fd1bc27a3d224b3d76db16190c1dd6d08)

## [v1.0.0](https://github.com/es-shims/String.prototype.trimEnd/compare/v0.1.0...v1.0.0) - 2020-03-30

### Commits

- [Breaking] convert to es-shim API [`2c6ef13`](https://github.com/es-shims/String.prototype.trimEnd/commit/2c6ef13d3f0b07a9bc55e367b311dbb731780405)
- [meta] add `auto-changelog` [`6f1fcc1`](https://github.com/es-shims/String.prototype.trimEnd/commit/6f1fcc1739de1e9541bd603b659807646a13dd7f)
- [meta] update readme [`ed4ce0d`](https://github.com/es-shims/String.prototype.trimEnd/commit/ed4ce0d84d53e626b48375c5959be20332464eaf)
- [Tests] add `npm run lint` [`eadaf2c`](https://github.com/es-shims/String.prototype.trimEnd/commit/eadaf2c83f2d791b54d80d7b30a9961ebc0f246f)
- Only apps should have lockfiles [`44d355f`](https://github.com/es-shims/String.prototype.trimEnd/commit/44d355f7dafcb0b51c5001824b07f7a2b9f1d06e)
- [actions] add automatic rebasing / merge commit blocking [`e78bf8e`](https://github.com/es-shims/String.prototype.trimEnd/commit/e78bf8e5fc04fcb3379dd1c98360d7df4f9ea7d6)
- [Tests] use shared travis-ci configs [`983c563`](https://github.com/es-shims/String.prototype.trimEnd/commit/983c5639efca2c9bb8b93ebbb917fbcb2561b94c)
- [meta] add `funding` field [`35139d6`](https://github.com/es-shims/String.prototype.trimEnd/commit/35139d6236ceacfc1501d08fb196d18a936ee583)
- [meta] fix non-updated version number [`a2d308b`](https://github.com/es-shims/String.prototype.trimEnd/commit/a2d308b99967ca427936c54747175794ca7336e1)

## [v0.1.0](https://github.com/es-shims/String.prototype.trimEnd/compare/v0.0.1...v0.1.0) - 2017-12-19

### Commits

- updated README [`f1c71a0`](https://github.com/es-shims/String.prototype.trimEnd/commit/f1c71a0a882e89e1c207ed2b316d91670be2b075)

## v0.0.1 - 2017-12-19

### Commits

- finished polyfill [`e58d550`](https://github.com/es-shims/String.prototype.trimEnd/commit/e58d550ab8695924ff4221ebe91f00f29801aa4b)
- created README file [`f78628a`](https://github.com/es-shims/String.prototype.trimEnd/commit/f78628ab123171f8b7759bba331d6a589702584f)
- Initial commit [`9199478`](https://github.com/es-shims/String.prototype.trimEnd/commit/9199478256da953e2f5bddfc4d82a161f4537e85)
- typo [`d1f4558`](https://github.com/es-shims/String.prototype.trimEnd/commit/d1f4558a51157833f14d8a424426d038d06576ce)
