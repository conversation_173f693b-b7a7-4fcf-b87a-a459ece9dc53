"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.dom = void 0;
const base_config_1 = require("./base-config");
exports.dom = {
    AddEventListenerOptions: base_config_1.TYPE,
    AesCbcParams: base_config_1.TYPE,
    AesCtrParams: base_config_1.TYPE,
    AesDerivedKeyParams: base_config_1.TYPE,
    AesGcmParams: base_config_1.TYPE,
    AesKeyAlgorithm: base_config_1.TYPE,
    AesKeyGenParams: base_config_1.TYPE,
    Algorithm: base_config_1.TYPE,
    AnalyserOptions: base_config_1.TYPE,
    AnimationEventInit: base_config_1.TYPE,
    AnimationPlaybackEventInit: base_config_1.TYPE,
    AssignedNodesOptions: base_config_1.TYPE,
    AudioBufferOptions: base_config_1.TYPE,
    AudioBufferSourceOptions: base_config_1.TYPE,
    AudioConfiguration: base_config_1.TYPE,
    AudioContextOptions: base_config_1.TYPE,
    AudioNodeOptions: base_config_1.TYPE,
    AudioProcessingEventInit: base_config_1.TYPE,
    AudioTimestamp: base_config_1.TYPE,
    AudioWorkletNodeOptions: base_config_1.TYPE,
    AuthenticationExtensionsClientInputs: base_config_1.TYPE,
    AuthenticationExtensionsClientOutputs: base_config_1.TYPE,
    AuthenticatorSelectionCriteria: base_config_1.TYPE,
    BiquadFilterOptions: base_config_1.TYPE,
    BlobEventInit: base_config_1.TYPE,
    BlobPropertyBag: base_config_1.TYPE,
    CSSStyleSheetInit: base_config_1.TYPE,
    CacheQueryOptions: base_config_1.TYPE,
    CanvasRenderingContext2DSettings: base_config_1.TYPE,
    ChannelMergerOptions: base_config_1.TYPE,
    ChannelSplitterOptions: base_config_1.TYPE,
    CheckVisibilityOptions: base_config_1.TYPE,
    ClientQueryOptions: base_config_1.TYPE,
    ClipboardEventInit: base_config_1.TYPE,
    ClipboardItemOptions: base_config_1.TYPE,
    CloseEventInit: base_config_1.TYPE,
    CompositionEventInit: base_config_1.TYPE,
    ComputedEffectTiming: base_config_1.TYPE,
    ComputedKeyframe: base_config_1.TYPE,
    ConstantSourceOptions: base_config_1.TYPE,
    ConstrainBooleanParameters: base_config_1.TYPE,
    ConstrainDOMStringParameters: base_config_1.TYPE,
    ConstrainDoubleRange: base_config_1.TYPE,
    ConstrainULongRange: base_config_1.TYPE,
    ConvolverOptions: base_config_1.TYPE,
    CredentialCreationOptions: base_config_1.TYPE,
    CredentialPropertiesOutput: base_config_1.TYPE,
    CredentialRequestOptions: base_config_1.TYPE,
    CryptoKeyPair: base_config_1.TYPE,
    CustomEventInit: base_config_1.TYPE,
    DOMMatrix2DInit: base_config_1.TYPE,
    DOMMatrixInit: base_config_1.TYPE,
    DOMPointInit: base_config_1.TYPE,
    DOMQuadInit: base_config_1.TYPE,
    DOMRectInit: base_config_1.TYPE,
    DelayOptions: base_config_1.TYPE,
    DeviceMotionEventAccelerationInit: base_config_1.TYPE,
    DeviceMotionEventInit: base_config_1.TYPE,
    DeviceMotionEventRotationRateInit: base_config_1.TYPE,
    DeviceOrientationEventInit: base_config_1.TYPE,
    DisplayMediaStreamOptions: base_config_1.TYPE,
    DocumentTimelineOptions: base_config_1.TYPE,
    DoubleRange: base_config_1.TYPE,
    DragEventInit: base_config_1.TYPE,
    DynamicsCompressorOptions: base_config_1.TYPE,
    EcKeyAlgorithm: base_config_1.TYPE,
    EcKeyGenParams: base_config_1.TYPE,
    EcKeyImportParams: base_config_1.TYPE,
    EcdhKeyDeriveParams: base_config_1.TYPE,
    EcdsaParams: base_config_1.TYPE,
    EffectTiming: base_config_1.TYPE,
    ElementCreationOptions: base_config_1.TYPE,
    ElementDefinitionOptions: base_config_1.TYPE,
    ErrorEventInit: base_config_1.TYPE,
    EventInit: base_config_1.TYPE,
    EventListenerOptions: base_config_1.TYPE,
    EventModifierInit: base_config_1.TYPE,
    EventSourceInit: base_config_1.TYPE,
    FilePropertyBag: base_config_1.TYPE,
    FileSystemFlags: base_config_1.TYPE,
    FileSystemGetDirectoryOptions: base_config_1.TYPE,
    FileSystemGetFileOptions: base_config_1.TYPE,
    FileSystemRemoveOptions: base_config_1.TYPE,
    FocusEventInit: base_config_1.TYPE,
    FocusOptions: base_config_1.TYPE,
    FontFaceDescriptors: base_config_1.TYPE,
    FontFaceSetLoadEventInit: base_config_1.TYPE,
    FormDataEventInit: base_config_1.TYPE,
    FullscreenOptions: base_config_1.TYPE,
    GainOptions: base_config_1.TYPE,
    GamepadEventInit: base_config_1.TYPE,
    GetAnimationsOptions: base_config_1.TYPE,
    GetNotificationOptions: base_config_1.TYPE,
    GetRootNodeOptions: base_config_1.TYPE,
    HashChangeEventInit: base_config_1.TYPE,
    HkdfParams: base_config_1.TYPE,
    HmacImportParams: base_config_1.TYPE,
    HmacKeyAlgorithm: base_config_1.TYPE,
    HmacKeyGenParams: base_config_1.TYPE,
    IDBDatabaseInfo: base_config_1.TYPE,
    IDBIndexParameters: base_config_1.TYPE,
    IDBObjectStoreParameters: base_config_1.TYPE,
    IDBTransactionOptions: base_config_1.TYPE,
    IDBVersionChangeEventInit: base_config_1.TYPE,
    IIRFilterOptions: base_config_1.TYPE,
    IdleRequestOptions: base_config_1.TYPE,
    ImageBitmapOptions: base_config_1.TYPE,
    ImageBitmapRenderingContextSettings: base_config_1.TYPE,
    ImageDataSettings: base_config_1.TYPE,
    ImageEncodeOptions: base_config_1.TYPE,
    ImportMeta: base_config_1.TYPE,
    InputEventInit: base_config_1.TYPE,
    IntersectionObserverEntryInit: base_config_1.TYPE,
    IntersectionObserverInit: base_config_1.TYPE,
    JsonWebKey: base_config_1.TYPE,
    KeyAlgorithm: base_config_1.TYPE,
    KeyboardEventInit: base_config_1.TYPE,
    Keyframe: base_config_1.TYPE,
    KeyframeAnimationOptions: base_config_1.TYPE,
    KeyframeEffectOptions: base_config_1.TYPE,
    LockInfo: base_config_1.TYPE,
    LockManagerSnapshot: base_config_1.TYPE,
    LockOptions: base_config_1.TYPE,
    MIDIConnectionEventInit: base_config_1.TYPE,
    MIDIMessageEventInit: base_config_1.TYPE,
    MIDIOptions: base_config_1.TYPE,
    MediaCapabilitiesDecodingInfo: base_config_1.TYPE,
    MediaCapabilitiesEncodingInfo: base_config_1.TYPE,
    MediaCapabilitiesInfo: base_config_1.TYPE,
    MediaConfiguration: base_config_1.TYPE,
    MediaDecodingConfiguration: base_config_1.TYPE,
    MediaElementAudioSourceOptions: base_config_1.TYPE,
    MediaEncodingConfiguration: base_config_1.TYPE,
    MediaEncryptedEventInit: base_config_1.TYPE,
    MediaImage: base_config_1.TYPE,
    MediaKeyMessageEventInit: base_config_1.TYPE,
    MediaKeySystemConfiguration: base_config_1.TYPE,
    MediaKeySystemMediaCapability: base_config_1.TYPE,
    MediaMetadataInit: base_config_1.TYPE,
    MediaPositionState: base_config_1.TYPE,
    MediaQueryListEventInit: base_config_1.TYPE,
    MediaRecorderOptions: base_config_1.TYPE,
    MediaSessionActionDetails: base_config_1.TYPE,
    MediaStreamAudioSourceOptions: base_config_1.TYPE,
    MediaStreamConstraints: base_config_1.TYPE,
    MediaStreamTrackEventInit: base_config_1.TYPE,
    MediaTrackCapabilities: base_config_1.TYPE,
    MediaTrackConstraintSet: base_config_1.TYPE,
    MediaTrackConstraints: base_config_1.TYPE,
    MediaTrackSettings: base_config_1.TYPE,
    MediaTrackSupportedConstraints: base_config_1.TYPE,
    MessageEventInit: base_config_1.TYPE,
    MouseEventInit: base_config_1.TYPE,
    MultiCacheQueryOptions: base_config_1.TYPE,
    MutationObserverInit: base_config_1.TYPE,
    NavigationPreloadState: base_config_1.TYPE,
    NotificationAction: base_config_1.TYPE,
    NotificationOptions: base_config_1.TYPE,
    OfflineAudioCompletionEventInit: base_config_1.TYPE,
    OfflineAudioContextOptions: base_config_1.TYPE,
    OptionalEffectTiming: base_config_1.TYPE,
    OscillatorOptions: base_config_1.TYPE,
    PageTransitionEventInit: base_config_1.TYPE,
    PannerOptions: base_config_1.TYPE,
    PaymentCurrencyAmount: base_config_1.TYPE,
    PaymentDetailsBase: base_config_1.TYPE,
    PaymentDetailsInit: base_config_1.TYPE,
    PaymentDetailsModifier: base_config_1.TYPE,
    PaymentDetailsUpdate: base_config_1.TYPE,
    PaymentItem: base_config_1.TYPE,
    PaymentMethodChangeEventInit: base_config_1.TYPE,
    PaymentMethodData: base_config_1.TYPE,
    PaymentRequestUpdateEventInit: base_config_1.TYPE,
    PaymentValidationErrors: base_config_1.TYPE,
    Pbkdf2Params: base_config_1.TYPE,
    PerformanceMarkOptions: base_config_1.TYPE,
    PerformanceMeasureOptions: base_config_1.TYPE,
    PerformanceObserverInit: base_config_1.TYPE,
    PeriodicWaveConstraints: base_config_1.TYPE,
    PeriodicWaveOptions: base_config_1.TYPE,
    PermissionDescriptor: base_config_1.TYPE,
    PictureInPictureEventInit: base_config_1.TYPE,
    PointerEventInit: base_config_1.TYPE,
    PopStateEventInit: base_config_1.TYPE,
    PositionOptions: base_config_1.TYPE,
    ProgressEventInit: base_config_1.TYPE,
    PromiseRejectionEventInit: base_config_1.TYPE,
    PropertyIndexedKeyframes: base_config_1.TYPE,
    PublicKeyCredentialCreationOptions: base_config_1.TYPE,
    PublicKeyCredentialDescriptor: base_config_1.TYPE,
    PublicKeyCredentialEntity: base_config_1.TYPE,
    PublicKeyCredentialParameters: base_config_1.TYPE,
    PublicKeyCredentialRequestOptions: base_config_1.TYPE,
    PublicKeyCredentialRpEntity: base_config_1.TYPE,
    PublicKeyCredentialUserEntity: base_config_1.TYPE,
    PushSubscriptionJSON: base_config_1.TYPE,
    PushSubscriptionOptionsInit: base_config_1.TYPE,
    QueuingStrategy: base_config_1.TYPE,
    QueuingStrategyInit: base_config_1.TYPE,
    RTCAnswerOptions: base_config_1.TYPE,
    RTCCertificateExpiration: base_config_1.TYPE,
    RTCConfiguration: base_config_1.TYPE,
    RTCDTMFToneChangeEventInit: base_config_1.TYPE,
    RTCDataChannelEventInit: base_config_1.TYPE,
    RTCDataChannelInit: base_config_1.TYPE,
    RTCDtlsFingerprint: base_config_1.TYPE,
    RTCEncodedAudioFrameMetadata: base_config_1.TYPE,
    RTCEncodedVideoFrameMetadata: base_config_1.TYPE,
    RTCErrorEventInit: base_config_1.TYPE,
    RTCErrorInit: base_config_1.TYPE,
    RTCIceCandidateInit: base_config_1.TYPE,
    RTCIceCandidatePairStats: base_config_1.TYPE,
    RTCIceServer: base_config_1.TYPE,
    RTCInboundRtpStreamStats: base_config_1.TYPE,
    RTCLocalSessionDescriptionInit: base_config_1.TYPE,
    RTCOfferAnswerOptions: base_config_1.TYPE,
    RTCOfferOptions: base_config_1.TYPE,
    RTCOutboundRtpStreamStats: base_config_1.TYPE,
    RTCPeerConnectionIceErrorEventInit: base_config_1.TYPE,
    RTCPeerConnectionIceEventInit: base_config_1.TYPE,
    RTCReceivedRtpStreamStats: base_config_1.TYPE,
    RTCRtcpParameters: base_config_1.TYPE,
    RTCRtpCapabilities: base_config_1.TYPE,
    RTCRtpCodecCapability: base_config_1.TYPE,
    RTCRtpCodecParameters: base_config_1.TYPE,
    RTCRtpCodingParameters: base_config_1.TYPE,
    RTCRtpContributingSource: base_config_1.TYPE,
    RTCRtpEncodingParameters: base_config_1.TYPE,
    RTCRtpHeaderExtensionCapability: base_config_1.TYPE,
    RTCRtpHeaderExtensionParameters: base_config_1.TYPE,
    RTCRtpParameters: base_config_1.TYPE,
    RTCRtpReceiveParameters: base_config_1.TYPE,
    RTCRtpSendParameters: base_config_1.TYPE,
    RTCRtpStreamStats: base_config_1.TYPE,
    RTCRtpSynchronizationSource: base_config_1.TYPE,
    RTCRtpTransceiverInit: base_config_1.TYPE,
    RTCSentRtpStreamStats: base_config_1.TYPE,
    RTCSessionDescriptionInit: base_config_1.TYPE,
    RTCStats: base_config_1.TYPE,
    RTCTrackEventInit: base_config_1.TYPE,
    RTCTransportStats: base_config_1.TYPE,
    ReadableStreamGetReaderOptions: base_config_1.TYPE,
    ReadableStreamReadDoneResult: base_config_1.TYPE,
    ReadableStreamReadValueResult: base_config_1.TYPE,
    ReadableWritablePair: base_config_1.TYPE,
    RegistrationOptions: base_config_1.TYPE,
    RequestInit: base_config_1.TYPE,
    ResizeObserverOptions: base_config_1.TYPE,
    ResponseInit: base_config_1.TYPE,
    RsaHashedImportParams: base_config_1.TYPE,
    RsaHashedKeyAlgorithm: base_config_1.TYPE,
    RsaHashedKeyGenParams: base_config_1.TYPE,
    RsaKeyAlgorithm: base_config_1.TYPE,
    RsaKeyGenParams: base_config_1.TYPE,
    RsaOaepParams: base_config_1.TYPE,
    RsaOtherPrimesInfo: base_config_1.TYPE,
    RsaPssParams: base_config_1.TYPE,
    SVGBoundingBoxOptions: base_config_1.TYPE,
    ScrollIntoViewOptions: base_config_1.TYPE,
    ScrollOptions: base_config_1.TYPE,
    ScrollToOptions: base_config_1.TYPE,
    SecurityPolicyViolationEventInit: base_config_1.TYPE,
    ShadowRootInit: base_config_1.TYPE,
    ShareData: base_config_1.TYPE,
    SpeechSynthesisErrorEventInit: base_config_1.TYPE,
    SpeechSynthesisEventInit: base_config_1.TYPE,
    StaticRangeInit: base_config_1.TYPE,
    StereoPannerOptions: base_config_1.TYPE,
    StorageEstimate: base_config_1.TYPE,
    StorageEventInit: base_config_1.TYPE,
    StreamPipeOptions: base_config_1.TYPE,
    StructuredSerializeOptions: base_config_1.TYPE,
    SubmitEventInit: base_config_1.TYPE,
    TextDecodeOptions: base_config_1.TYPE,
    TextDecoderOptions: base_config_1.TYPE,
    TextEncoderEncodeIntoResult: base_config_1.TYPE,
    TouchEventInit: base_config_1.TYPE,
    TouchInit: base_config_1.TYPE,
    TrackEventInit: base_config_1.TYPE,
    Transformer: base_config_1.TYPE,
    TransitionEventInit: base_config_1.TYPE,
    UIEventInit: base_config_1.TYPE,
    ULongRange: base_config_1.TYPE,
    UnderlyingByteSource: base_config_1.TYPE,
    UnderlyingDefaultSource: base_config_1.TYPE,
    UnderlyingSink: base_config_1.TYPE,
    UnderlyingSource: base_config_1.TYPE,
    ValidityStateFlags: base_config_1.TYPE,
    VideoColorSpaceInit: base_config_1.TYPE,
    VideoConfiguration: base_config_1.TYPE,
    VideoFrameCallbackMetadata: base_config_1.TYPE,
    WaveShaperOptions: base_config_1.TYPE,
    WebGLContextAttributes: base_config_1.TYPE,
    WebGLContextEventInit: base_config_1.TYPE,
    WheelEventInit: base_config_1.TYPE,
    WindowPostMessageOptions: base_config_1.TYPE,
    WorkerOptions: base_config_1.TYPE,
    WorkletOptions: base_config_1.TYPE,
    NodeFilter: base_config_1.TYPE_VALUE,
    XPathNSResolver: base_config_1.TYPE,
    ANGLE_instanced_arrays: base_config_1.TYPE,
    ARIAMixin: base_config_1.TYPE,
    AbortController: base_config_1.TYPE_VALUE,
    AbortSignalEventMap: base_config_1.TYPE,
    AbortSignal: base_config_1.TYPE_VALUE,
    AbstractRange: base_config_1.TYPE_VALUE,
    AbstractWorkerEventMap: base_config_1.TYPE,
    AbstractWorker: base_config_1.TYPE,
    AnalyserNode: base_config_1.TYPE_VALUE,
    Animatable: base_config_1.TYPE,
    AnimationEventMap: base_config_1.TYPE,
    Animation: base_config_1.TYPE_VALUE,
    AnimationEffect: base_config_1.TYPE_VALUE,
    AnimationEvent: base_config_1.TYPE_VALUE,
    AnimationFrameProvider: base_config_1.TYPE,
    AnimationPlaybackEvent: base_config_1.TYPE_VALUE,
    AnimationTimeline: base_config_1.TYPE_VALUE,
    Attr: base_config_1.TYPE_VALUE,
    AudioBuffer: base_config_1.TYPE_VALUE,
    AudioBufferSourceNode: base_config_1.TYPE_VALUE,
    AudioContext: base_config_1.TYPE_VALUE,
    AudioDestinationNode: base_config_1.TYPE_VALUE,
    AudioListener: base_config_1.TYPE_VALUE,
    AudioNode: base_config_1.TYPE_VALUE,
    AudioParam: base_config_1.TYPE_VALUE,
    AudioParamMap: base_config_1.TYPE_VALUE,
    AudioProcessingEvent: base_config_1.TYPE_VALUE,
    AudioScheduledSourceNodeEventMap: base_config_1.TYPE,
    AudioScheduledSourceNode: base_config_1.TYPE_VALUE,
    AudioWorklet: base_config_1.TYPE_VALUE,
    AudioWorkletNodeEventMap: base_config_1.TYPE,
    AudioWorkletNode: base_config_1.TYPE_VALUE,
    AuthenticatorAssertionResponse: base_config_1.TYPE_VALUE,
    AuthenticatorAttestationResponse: base_config_1.TYPE_VALUE,
    AuthenticatorResponse: base_config_1.TYPE_VALUE,
    BarProp: base_config_1.TYPE_VALUE,
    BaseAudioContextEventMap: base_config_1.TYPE,
    BaseAudioContext: base_config_1.TYPE_VALUE,
    BeforeUnloadEvent: base_config_1.TYPE_VALUE,
    BiquadFilterNode: base_config_1.TYPE_VALUE,
    Blob: base_config_1.TYPE_VALUE,
    BlobEvent: base_config_1.TYPE_VALUE,
    Body: base_config_1.TYPE,
    BroadcastChannelEventMap: base_config_1.TYPE,
    BroadcastChannel: base_config_1.TYPE_VALUE,
    ByteLengthQueuingStrategy: base_config_1.TYPE_VALUE,
    CDATASection: base_config_1.TYPE_VALUE,
    CSSAnimation: base_config_1.TYPE_VALUE,
    CSSConditionRule: base_config_1.TYPE_VALUE,
    CSSContainerRule: base_config_1.TYPE_VALUE,
    CSSCounterStyleRule: base_config_1.TYPE_VALUE,
    CSSFontFaceRule: base_config_1.TYPE_VALUE,
    CSSFontFeatureValuesRule: base_config_1.TYPE_VALUE,
    CSSFontPaletteValuesRule: base_config_1.TYPE_VALUE,
    CSSGroupingRule: base_config_1.TYPE_VALUE,
    CSSImportRule: base_config_1.TYPE_VALUE,
    CSSKeyframeRule: base_config_1.TYPE_VALUE,
    CSSKeyframesRule: base_config_1.TYPE_VALUE,
    CSSLayerBlockRule: base_config_1.TYPE_VALUE,
    CSSLayerStatementRule: base_config_1.TYPE_VALUE,
    CSSMediaRule: base_config_1.TYPE_VALUE,
    CSSNamespaceRule: base_config_1.TYPE_VALUE,
    CSSPageRule: base_config_1.TYPE_VALUE,
    CSSRule: base_config_1.TYPE_VALUE,
    CSSRuleList: base_config_1.TYPE_VALUE,
    CSSStyleDeclaration: base_config_1.TYPE_VALUE,
    CSSStyleRule: base_config_1.TYPE_VALUE,
    CSSStyleSheet: base_config_1.TYPE_VALUE,
    CSSSupportsRule: base_config_1.TYPE_VALUE,
    CSSTransition: base_config_1.TYPE_VALUE,
    Cache: base_config_1.TYPE_VALUE,
    CacheStorage: base_config_1.TYPE_VALUE,
    CanvasCaptureMediaStreamTrack: base_config_1.TYPE_VALUE,
    CanvasCompositing: base_config_1.TYPE,
    CanvasDrawImage: base_config_1.TYPE,
    CanvasDrawPath: base_config_1.TYPE,
    CanvasFillStrokeStyles: base_config_1.TYPE,
    CanvasFilters: base_config_1.TYPE,
    CanvasGradient: base_config_1.TYPE_VALUE,
    CanvasImageData: base_config_1.TYPE,
    CanvasImageSmoothing: base_config_1.TYPE,
    CanvasPath: base_config_1.TYPE,
    CanvasPathDrawingStyles: base_config_1.TYPE,
    CanvasPattern: base_config_1.TYPE_VALUE,
    CanvasRect: base_config_1.TYPE,
    CanvasRenderingContext2D: base_config_1.TYPE_VALUE,
    CanvasShadowStyles: base_config_1.TYPE,
    CanvasState: base_config_1.TYPE,
    CanvasText: base_config_1.TYPE,
    CanvasTextDrawingStyles: base_config_1.TYPE,
    CanvasTransform: base_config_1.TYPE,
    CanvasUserInterface: base_config_1.TYPE,
    ChannelMergerNode: base_config_1.TYPE_VALUE,
    ChannelSplitterNode: base_config_1.TYPE_VALUE,
    CharacterData: base_config_1.TYPE_VALUE,
    ChildNode: base_config_1.TYPE,
    ClientRect: base_config_1.TYPE,
    Clipboard: base_config_1.TYPE_VALUE,
    ClipboardEvent: base_config_1.TYPE_VALUE,
    ClipboardItem: base_config_1.TYPE_VALUE,
    CloseEvent: base_config_1.TYPE_VALUE,
    Comment: base_config_1.TYPE_VALUE,
    CompositionEvent: base_config_1.TYPE_VALUE,
    ConstantSourceNode: base_config_1.TYPE_VALUE,
    ConvolverNode: base_config_1.TYPE_VALUE,
    CountQueuingStrategy: base_config_1.TYPE_VALUE,
    Credential: base_config_1.TYPE_VALUE,
    CredentialsContainer: base_config_1.TYPE_VALUE,
    Crypto: base_config_1.TYPE_VALUE,
    CryptoKey: base_config_1.TYPE_VALUE,
    CustomElementRegistry: base_config_1.TYPE_VALUE,
    CustomEvent: base_config_1.TYPE_VALUE,
    DOMException: base_config_1.TYPE_VALUE,
    DOMImplementation: base_config_1.TYPE_VALUE,
    DOMMatrix: base_config_1.TYPE_VALUE,
    SVGMatrix: base_config_1.TYPE_VALUE,
    WebKitCSSMatrix: base_config_1.TYPE_VALUE,
    DOMMatrixReadOnly: base_config_1.TYPE_VALUE,
    DOMParser: base_config_1.TYPE_VALUE,
    DOMPoint: base_config_1.TYPE_VALUE,
    SVGPoint: base_config_1.TYPE_VALUE,
    DOMPointReadOnly: base_config_1.TYPE_VALUE,
    DOMQuad: base_config_1.TYPE_VALUE,
    DOMRect: base_config_1.TYPE_VALUE,
    SVGRect: base_config_1.TYPE_VALUE,
    DOMRectList: base_config_1.TYPE_VALUE,
    DOMRectReadOnly: base_config_1.TYPE_VALUE,
    DOMStringList: base_config_1.TYPE_VALUE,
    DOMStringMap: base_config_1.TYPE_VALUE,
    DOMTokenList: base_config_1.TYPE_VALUE,
    DataTransfer: base_config_1.TYPE_VALUE,
    DataTransferItem: base_config_1.TYPE_VALUE,
    DataTransferItemList: base_config_1.TYPE_VALUE,
    DelayNode: base_config_1.TYPE_VALUE,
    DeviceMotionEvent: base_config_1.TYPE_VALUE,
    DeviceMotionEventAcceleration: base_config_1.TYPE,
    DeviceMotionEventRotationRate: base_config_1.TYPE,
    DeviceOrientationEvent: base_config_1.TYPE_VALUE,
    DocumentEventMap: base_config_1.TYPE,
    Document: base_config_1.TYPE_VALUE,
    DocumentFragment: base_config_1.TYPE_VALUE,
    DocumentOrShadowRoot: base_config_1.TYPE,
    DocumentTimeline: base_config_1.TYPE_VALUE,
    DocumentType: base_config_1.TYPE_VALUE,
    DragEvent: base_config_1.TYPE_VALUE,
    DynamicsCompressorNode: base_config_1.TYPE_VALUE,
    EXT_blend_minmax: base_config_1.TYPE,
    EXT_color_buffer_float: base_config_1.TYPE,
    EXT_color_buffer_half_float: base_config_1.TYPE,
    EXT_float_blend: base_config_1.TYPE,
    EXT_frag_depth: base_config_1.TYPE,
    EXT_sRGB: base_config_1.TYPE,
    EXT_shader_texture_lod: base_config_1.TYPE,
    EXT_texture_compression_bptc: base_config_1.TYPE,
    EXT_texture_compression_rgtc: base_config_1.TYPE,
    EXT_texture_filter_anisotropic: base_config_1.TYPE,
    EXT_texture_norm16: base_config_1.TYPE,
    ElementEventMap: base_config_1.TYPE,
    Element: base_config_1.TYPE_VALUE,
    ElementCSSInlineStyle: base_config_1.TYPE,
    ElementContentEditable: base_config_1.TYPE,
    ElementInternals: base_config_1.TYPE_VALUE,
    ErrorEvent: base_config_1.TYPE_VALUE,
    Event: base_config_1.TYPE_VALUE,
    EventCounts: base_config_1.TYPE_VALUE,
    EventListener: base_config_1.TYPE,
    EventListenerObject: base_config_1.TYPE,
    EventSourceEventMap: base_config_1.TYPE,
    EventSource: base_config_1.TYPE_VALUE,
    EventTarget: base_config_1.TYPE_VALUE,
    External: base_config_1.TYPE_VALUE,
    File: base_config_1.TYPE_VALUE,
    FileList: base_config_1.TYPE_VALUE,
    FileReaderEventMap: base_config_1.TYPE,
    FileReader: base_config_1.TYPE_VALUE,
    FileSystem: base_config_1.TYPE_VALUE,
    FileSystemDirectoryEntry: base_config_1.TYPE_VALUE,
    FileSystemDirectoryHandle: base_config_1.TYPE_VALUE,
    FileSystemDirectoryReader: base_config_1.TYPE_VALUE,
    FileSystemEntry: base_config_1.TYPE_VALUE,
    FileSystemFileEntry: base_config_1.TYPE_VALUE,
    FileSystemFileHandle: base_config_1.TYPE_VALUE,
    FileSystemHandle: base_config_1.TYPE_VALUE,
    FocusEvent: base_config_1.TYPE_VALUE,
    FontFace: base_config_1.TYPE_VALUE,
    FontFaceSetEventMap: base_config_1.TYPE,
    FontFaceSet: base_config_1.TYPE_VALUE,
    FontFaceSetLoadEvent: base_config_1.TYPE_VALUE,
    FontFaceSource: base_config_1.TYPE,
    FormData: base_config_1.TYPE_VALUE,
    FormDataEvent: base_config_1.TYPE_VALUE,
    GainNode: base_config_1.TYPE_VALUE,
    Gamepad: base_config_1.TYPE_VALUE,
    GamepadButton: base_config_1.TYPE_VALUE,
    GamepadEvent: base_config_1.TYPE_VALUE,
    GamepadHapticActuator: base_config_1.TYPE_VALUE,
    GenericTransformStream: base_config_1.TYPE,
    Geolocation: base_config_1.TYPE_VALUE,
    GeolocationCoordinates: base_config_1.TYPE_VALUE,
    GeolocationPosition: base_config_1.TYPE_VALUE,
    GeolocationPositionError: base_config_1.TYPE_VALUE,
    GlobalEventHandlersEventMap: base_config_1.TYPE,
    GlobalEventHandlers: base_config_1.TYPE,
    HTMLAllCollection: base_config_1.TYPE_VALUE,
    HTMLAnchorElement: base_config_1.TYPE_VALUE,
    HTMLAreaElement: base_config_1.TYPE_VALUE,
    HTMLAudioElement: base_config_1.TYPE_VALUE,
    HTMLBRElement: base_config_1.TYPE_VALUE,
    HTMLBaseElement: base_config_1.TYPE_VALUE,
    HTMLBodyElementEventMap: base_config_1.TYPE,
    HTMLBodyElement: base_config_1.TYPE_VALUE,
    HTMLButtonElement: base_config_1.TYPE_VALUE,
    HTMLCanvasElement: base_config_1.TYPE_VALUE,
    HTMLCollectionBase: base_config_1.TYPE,
    HTMLCollection: base_config_1.TYPE_VALUE,
    HTMLCollectionOf: base_config_1.TYPE,
    HTMLDListElement: base_config_1.TYPE_VALUE,
    HTMLDataElement: base_config_1.TYPE_VALUE,
    HTMLDataListElement: base_config_1.TYPE_VALUE,
    HTMLDetailsElement: base_config_1.TYPE_VALUE,
    HTMLDialogElement: base_config_1.TYPE_VALUE,
    HTMLDirectoryElement: base_config_1.TYPE_VALUE,
    HTMLDivElement: base_config_1.TYPE_VALUE,
    HTMLDocument: base_config_1.TYPE_VALUE,
    HTMLElementEventMap: base_config_1.TYPE,
    HTMLElement: base_config_1.TYPE_VALUE,
    HTMLEmbedElement: base_config_1.TYPE_VALUE,
    HTMLFieldSetElement: base_config_1.TYPE_VALUE,
    HTMLFontElement: base_config_1.TYPE_VALUE,
    HTMLFormControlsCollection: base_config_1.TYPE_VALUE,
    HTMLFormElement: base_config_1.TYPE_VALUE,
    HTMLFrameElement: base_config_1.TYPE_VALUE,
    HTMLFrameSetElementEventMap: base_config_1.TYPE,
    HTMLFrameSetElement: base_config_1.TYPE_VALUE,
    HTMLHRElement: base_config_1.TYPE_VALUE,
    HTMLHeadElement: base_config_1.TYPE_VALUE,
    HTMLHeadingElement: base_config_1.TYPE_VALUE,
    HTMLHtmlElement: base_config_1.TYPE_VALUE,
    HTMLHyperlinkElementUtils: base_config_1.TYPE,
    HTMLIFrameElement: base_config_1.TYPE_VALUE,
    HTMLImageElement: base_config_1.TYPE_VALUE,
    HTMLInputElement: base_config_1.TYPE_VALUE,
    HTMLLIElement: base_config_1.TYPE_VALUE,
    HTMLLabelElement: base_config_1.TYPE_VALUE,
    HTMLLegendElement: base_config_1.TYPE_VALUE,
    HTMLLinkElement: base_config_1.TYPE_VALUE,
    HTMLMapElement: base_config_1.TYPE_VALUE,
    HTMLMarqueeElement: base_config_1.TYPE_VALUE,
    HTMLMediaElementEventMap: base_config_1.TYPE,
    HTMLMediaElement: base_config_1.TYPE_VALUE,
    HTMLMenuElement: base_config_1.TYPE_VALUE,
    HTMLMetaElement: base_config_1.TYPE_VALUE,
    HTMLMeterElement: base_config_1.TYPE_VALUE,
    HTMLModElement: base_config_1.TYPE_VALUE,
    HTMLOListElement: base_config_1.TYPE_VALUE,
    HTMLObjectElement: base_config_1.TYPE_VALUE,
    HTMLOptGroupElement: base_config_1.TYPE_VALUE,
    HTMLOptionElement: base_config_1.TYPE_VALUE,
    HTMLOptionsCollection: base_config_1.TYPE_VALUE,
    HTMLOrSVGElement: base_config_1.TYPE,
    HTMLOutputElement: base_config_1.TYPE_VALUE,
    HTMLParagraphElement: base_config_1.TYPE_VALUE,
    HTMLParamElement: base_config_1.TYPE_VALUE,
    HTMLPictureElement: base_config_1.TYPE_VALUE,
    HTMLPreElement: base_config_1.TYPE_VALUE,
    HTMLProgressElement: base_config_1.TYPE_VALUE,
    HTMLQuoteElement: base_config_1.TYPE_VALUE,
    HTMLScriptElement: base_config_1.TYPE_VALUE,
    HTMLSelectElement: base_config_1.TYPE_VALUE,
    HTMLSlotElement: base_config_1.TYPE_VALUE,
    HTMLSourceElement: base_config_1.TYPE_VALUE,
    HTMLSpanElement: base_config_1.TYPE_VALUE,
    HTMLStyleElement: base_config_1.TYPE_VALUE,
    HTMLTableCaptionElement: base_config_1.TYPE_VALUE,
    HTMLTableCellElement: base_config_1.TYPE_VALUE,
    HTMLTableColElement: base_config_1.TYPE_VALUE,
    HTMLTableDataCellElement: base_config_1.TYPE,
    HTMLTableElement: base_config_1.TYPE_VALUE,
    HTMLTableHeaderCellElement: base_config_1.TYPE,
    HTMLTableRowElement: base_config_1.TYPE_VALUE,
    HTMLTableSectionElement: base_config_1.TYPE_VALUE,
    HTMLTemplateElement: base_config_1.TYPE_VALUE,
    HTMLTextAreaElement: base_config_1.TYPE_VALUE,
    HTMLTimeElement: base_config_1.TYPE_VALUE,
    HTMLTitleElement: base_config_1.TYPE_VALUE,
    HTMLTrackElement: base_config_1.TYPE_VALUE,
    HTMLUListElement: base_config_1.TYPE_VALUE,
    HTMLUnknownElement: base_config_1.TYPE_VALUE,
    HTMLVideoElementEventMap: base_config_1.TYPE,
    HTMLVideoElement: base_config_1.TYPE_VALUE,
    HashChangeEvent: base_config_1.TYPE_VALUE,
    Headers: base_config_1.TYPE_VALUE,
    History: base_config_1.TYPE_VALUE,
    IDBCursor: base_config_1.TYPE_VALUE,
    IDBCursorWithValue: base_config_1.TYPE_VALUE,
    IDBDatabaseEventMap: base_config_1.TYPE,
    IDBDatabase: base_config_1.TYPE_VALUE,
    IDBFactory: base_config_1.TYPE_VALUE,
    IDBIndex: base_config_1.TYPE_VALUE,
    IDBKeyRange: base_config_1.TYPE_VALUE,
    IDBObjectStore: base_config_1.TYPE_VALUE,
    IDBOpenDBRequestEventMap: base_config_1.TYPE,
    IDBOpenDBRequest: base_config_1.TYPE_VALUE,
    IDBRequestEventMap: base_config_1.TYPE,
    IDBRequest: base_config_1.TYPE_VALUE,
    IDBTransactionEventMap: base_config_1.TYPE,
    IDBTransaction: base_config_1.TYPE_VALUE,
    IDBVersionChangeEvent: base_config_1.TYPE_VALUE,
    IIRFilterNode: base_config_1.TYPE_VALUE,
    IdleDeadline: base_config_1.TYPE_VALUE,
    ImageBitmap: base_config_1.TYPE_VALUE,
    ImageBitmapRenderingContext: base_config_1.TYPE_VALUE,
    ImageData: base_config_1.TYPE_VALUE,
    InnerHTML: base_config_1.TYPE,
    InputDeviceInfo: base_config_1.TYPE_VALUE,
    InputEvent: base_config_1.TYPE_VALUE,
    IntersectionObserver: base_config_1.TYPE_VALUE,
    IntersectionObserverEntry: base_config_1.TYPE_VALUE,
    KHR_parallel_shader_compile: base_config_1.TYPE,
    KeyboardEvent: base_config_1.TYPE_VALUE,
    KeyframeEffect: base_config_1.TYPE_VALUE,
    LinkStyle: base_config_1.TYPE,
    Location: base_config_1.TYPE_VALUE,
    Lock: base_config_1.TYPE_VALUE,
    LockManager: base_config_1.TYPE_VALUE,
    MIDIAccessEventMap: base_config_1.TYPE,
    MIDIAccess: base_config_1.TYPE_VALUE,
    MIDIConnectionEvent: base_config_1.TYPE_VALUE,
    MIDIInputEventMap: base_config_1.TYPE,
    MIDIInput: base_config_1.TYPE_VALUE,
    MIDIInputMap: base_config_1.TYPE_VALUE,
    MIDIMessageEvent: base_config_1.TYPE_VALUE,
    MIDIOutput: base_config_1.TYPE_VALUE,
    MIDIOutputMap: base_config_1.TYPE_VALUE,
    MIDIPortEventMap: base_config_1.TYPE,
    MIDIPort: base_config_1.TYPE_VALUE,
    MathMLElementEventMap: base_config_1.TYPE,
    MathMLElement: base_config_1.TYPE_VALUE,
    MediaCapabilities: base_config_1.TYPE_VALUE,
    MediaDeviceInfo: base_config_1.TYPE_VALUE,
    MediaDevicesEventMap: base_config_1.TYPE,
    MediaDevices: base_config_1.TYPE_VALUE,
    MediaElementAudioSourceNode: base_config_1.TYPE_VALUE,
    MediaEncryptedEvent: base_config_1.TYPE_VALUE,
    MediaError: base_config_1.TYPE_VALUE,
    MediaKeyMessageEvent: base_config_1.TYPE_VALUE,
    MediaKeySessionEventMap: base_config_1.TYPE,
    MediaKeySession: base_config_1.TYPE_VALUE,
    MediaKeyStatusMap: base_config_1.TYPE_VALUE,
    MediaKeySystemAccess: base_config_1.TYPE_VALUE,
    MediaKeys: base_config_1.TYPE_VALUE,
    MediaList: base_config_1.TYPE_VALUE,
    MediaMetadata: base_config_1.TYPE_VALUE,
    MediaQueryListEventMap: base_config_1.TYPE,
    MediaQueryList: base_config_1.TYPE_VALUE,
    MediaQueryListEvent: base_config_1.TYPE_VALUE,
    MediaRecorderEventMap: base_config_1.TYPE,
    MediaRecorder: base_config_1.TYPE_VALUE,
    MediaSession: base_config_1.TYPE_VALUE,
    MediaSourceEventMap: base_config_1.TYPE,
    MediaSource: base_config_1.TYPE_VALUE,
    MediaStreamEventMap: base_config_1.TYPE,
    MediaStream: base_config_1.TYPE_VALUE,
    MediaStreamAudioDestinationNode: base_config_1.TYPE_VALUE,
    MediaStreamAudioSourceNode: base_config_1.TYPE_VALUE,
    MediaStreamTrackEventMap: base_config_1.TYPE,
    MediaStreamTrack: base_config_1.TYPE_VALUE,
    MediaStreamTrackEvent: base_config_1.TYPE_VALUE,
    MessageChannel: base_config_1.TYPE_VALUE,
    MessageEvent: base_config_1.TYPE_VALUE,
    MessagePortEventMap: base_config_1.TYPE,
    MessagePort: base_config_1.TYPE_VALUE,
    MimeType: base_config_1.TYPE_VALUE,
    MimeTypeArray: base_config_1.TYPE_VALUE,
    MouseEvent: base_config_1.TYPE_VALUE,
    MutationEvent: base_config_1.TYPE_VALUE,
    MutationObserver: base_config_1.TYPE_VALUE,
    MutationRecord: base_config_1.TYPE_VALUE,
    NamedNodeMap: base_config_1.TYPE_VALUE,
    NavigationPreloadManager: base_config_1.TYPE_VALUE,
    Navigator: base_config_1.TYPE_VALUE,
    NavigatorAutomationInformation: base_config_1.TYPE,
    NavigatorConcurrentHardware: base_config_1.TYPE,
    NavigatorContentUtils: base_config_1.TYPE,
    NavigatorCookies: base_config_1.TYPE,
    NavigatorID: base_config_1.TYPE,
    NavigatorLanguage: base_config_1.TYPE,
    NavigatorLocks: base_config_1.TYPE,
    NavigatorOnLine: base_config_1.TYPE,
    NavigatorPlugins: base_config_1.TYPE,
    NavigatorStorage: base_config_1.TYPE,
    Node: base_config_1.TYPE_VALUE,
    NodeIterator: base_config_1.TYPE_VALUE,
    NodeList: base_config_1.TYPE_VALUE,
    NodeListOf: base_config_1.TYPE,
    NonDocumentTypeChildNode: base_config_1.TYPE,
    NonElementParentNode: base_config_1.TYPE,
    NotificationEventMap: base_config_1.TYPE,
    Notification: base_config_1.TYPE_VALUE,
    OES_draw_buffers_indexed: base_config_1.TYPE,
    OES_element_index_uint: base_config_1.TYPE,
    OES_fbo_render_mipmap: base_config_1.TYPE,
    OES_standard_derivatives: base_config_1.TYPE,
    OES_texture_float: base_config_1.TYPE,
    OES_texture_float_linear: base_config_1.TYPE,
    OES_texture_half_float: base_config_1.TYPE,
    OES_texture_half_float_linear: base_config_1.TYPE,
    OES_vertex_array_object: base_config_1.TYPE,
    OVR_multiview2: base_config_1.TYPE,
    OfflineAudioCompletionEvent: base_config_1.TYPE_VALUE,
    OfflineAudioContextEventMap: base_config_1.TYPE,
    OfflineAudioContext: base_config_1.TYPE_VALUE,
    OffscreenCanvasEventMap: base_config_1.TYPE,
    OffscreenCanvas: base_config_1.TYPE_VALUE,
    OffscreenCanvasRenderingContext2D: base_config_1.TYPE_VALUE,
    OscillatorNode: base_config_1.TYPE_VALUE,
    OverconstrainedError: base_config_1.TYPE_VALUE,
    PageTransitionEvent: base_config_1.TYPE_VALUE,
    PannerNode: base_config_1.TYPE_VALUE,
    ParentNode: base_config_1.TYPE,
    Path2D: base_config_1.TYPE_VALUE,
    PaymentMethodChangeEvent: base_config_1.TYPE_VALUE,
    PaymentRequestEventMap: base_config_1.TYPE,
    PaymentRequest: base_config_1.TYPE_VALUE,
    PaymentRequestUpdateEvent: base_config_1.TYPE_VALUE,
    PaymentResponse: base_config_1.TYPE_VALUE,
    PerformanceEventMap: base_config_1.TYPE,
    Performance: base_config_1.TYPE_VALUE,
    PerformanceEntry: base_config_1.TYPE_VALUE,
    PerformanceEventTiming: base_config_1.TYPE_VALUE,
    PerformanceMark: base_config_1.TYPE_VALUE,
    PerformanceMeasure: base_config_1.TYPE_VALUE,
    PerformanceNavigation: base_config_1.TYPE_VALUE,
    PerformanceNavigationTiming: base_config_1.TYPE_VALUE,
    PerformanceObserver: base_config_1.TYPE_VALUE,
    PerformanceObserverEntryList: base_config_1.TYPE_VALUE,
    PerformancePaintTiming: base_config_1.TYPE_VALUE,
    PerformanceResourceTiming: base_config_1.TYPE_VALUE,
    PerformanceServerTiming: base_config_1.TYPE_VALUE,
    PerformanceTiming: base_config_1.TYPE_VALUE,
    PeriodicWave: base_config_1.TYPE_VALUE,
    PermissionStatusEventMap: base_config_1.TYPE,
    PermissionStatus: base_config_1.TYPE_VALUE,
    Permissions: base_config_1.TYPE_VALUE,
    PictureInPictureEvent: base_config_1.TYPE_VALUE,
    PictureInPictureWindowEventMap: base_config_1.TYPE,
    PictureInPictureWindow: base_config_1.TYPE_VALUE,
    Plugin: base_config_1.TYPE_VALUE,
    PluginArray: base_config_1.TYPE_VALUE,
    PointerEvent: base_config_1.TYPE_VALUE,
    PopStateEvent: base_config_1.TYPE_VALUE,
    ProcessingInstruction: base_config_1.TYPE_VALUE,
    ProgressEvent: base_config_1.TYPE_VALUE,
    PromiseRejectionEvent: base_config_1.TYPE_VALUE,
    PublicKeyCredential: base_config_1.TYPE_VALUE,
    PushManager: base_config_1.TYPE_VALUE,
    PushSubscription: base_config_1.TYPE_VALUE,
    PushSubscriptionOptions: base_config_1.TYPE_VALUE,
    RTCCertificate: base_config_1.TYPE_VALUE,
    RTCDTMFSenderEventMap: base_config_1.TYPE,
    RTCDTMFSender: base_config_1.TYPE_VALUE,
    RTCDTMFToneChangeEvent: base_config_1.TYPE_VALUE,
    RTCDataChannelEventMap: base_config_1.TYPE,
    RTCDataChannel: base_config_1.TYPE_VALUE,
    RTCDataChannelEvent: base_config_1.TYPE_VALUE,
    RTCDtlsTransportEventMap: base_config_1.TYPE,
    RTCDtlsTransport: base_config_1.TYPE_VALUE,
    RTCEncodedAudioFrame: base_config_1.TYPE_VALUE,
    RTCEncodedVideoFrame: base_config_1.TYPE_VALUE,
    RTCError: base_config_1.TYPE_VALUE,
    RTCErrorEvent: base_config_1.TYPE_VALUE,
    RTCIceCandidate: base_config_1.TYPE_VALUE,
    RTCIceTransportEventMap: base_config_1.TYPE,
    RTCIceTransport: base_config_1.TYPE_VALUE,
    RTCPeerConnectionEventMap: base_config_1.TYPE,
    RTCPeerConnection: base_config_1.TYPE_VALUE,
    RTCPeerConnectionIceErrorEvent: base_config_1.TYPE_VALUE,
    RTCPeerConnectionIceEvent: base_config_1.TYPE_VALUE,
    RTCRtpReceiver: base_config_1.TYPE_VALUE,
    RTCRtpSender: base_config_1.TYPE_VALUE,
    RTCRtpTransceiver: base_config_1.TYPE_VALUE,
    RTCSctpTransportEventMap: base_config_1.TYPE,
    RTCSctpTransport: base_config_1.TYPE_VALUE,
    RTCSessionDescription: base_config_1.TYPE_VALUE,
    RTCStatsReport: base_config_1.TYPE_VALUE,
    RTCTrackEvent: base_config_1.TYPE_VALUE,
    RadioNodeList: base_config_1.TYPE_VALUE,
    Range: base_config_1.TYPE_VALUE,
    ReadableByteStreamController: base_config_1.TYPE_VALUE,
    ReadableStream: base_config_1.TYPE_VALUE,
    ReadableStreamBYOBReader: base_config_1.TYPE_VALUE,
    ReadableStreamBYOBRequest: base_config_1.TYPE_VALUE,
    ReadableStreamDefaultController: base_config_1.TYPE_VALUE,
    ReadableStreamDefaultReader: base_config_1.TYPE_VALUE,
    ReadableStreamGenericReader: base_config_1.TYPE,
    RemotePlaybackEventMap: base_config_1.TYPE,
    RemotePlayback: base_config_1.TYPE_VALUE,
    Request: base_config_1.TYPE_VALUE,
    ResizeObserver: base_config_1.TYPE_VALUE,
    ResizeObserverEntry: base_config_1.TYPE_VALUE,
    ResizeObserverSize: base_config_1.TYPE_VALUE,
    Response: base_config_1.TYPE_VALUE,
    SVGAElement: base_config_1.TYPE_VALUE,
    SVGAngle: base_config_1.TYPE_VALUE,
    SVGAnimateElement: base_config_1.TYPE_VALUE,
    SVGAnimateMotionElement: base_config_1.TYPE_VALUE,
    SVGAnimateTransformElement: base_config_1.TYPE_VALUE,
    SVGAnimatedAngle: base_config_1.TYPE_VALUE,
    SVGAnimatedBoolean: base_config_1.TYPE_VALUE,
    SVGAnimatedEnumeration: base_config_1.TYPE_VALUE,
    SVGAnimatedInteger: base_config_1.TYPE_VALUE,
    SVGAnimatedLength: base_config_1.TYPE_VALUE,
    SVGAnimatedLengthList: base_config_1.TYPE_VALUE,
    SVGAnimatedNumber: base_config_1.TYPE_VALUE,
    SVGAnimatedNumberList: base_config_1.TYPE_VALUE,
    SVGAnimatedPoints: base_config_1.TYPE,
    SVGAnimatedPreserveAspectRatio: base_config_1.TYPE_VALUE,
    SVGAnimatedRect: base_config_1.TYPE_VALUE,
    SVGAnimatedString: base_config_1.TYPE_VALUE,
    SVGAnimatedTransformList: base_config_1.TYPE_VALUE,
    SVGAnimationElement: base_config_1.TYPE_VALUE,
    SVGCircleElement: base_config_1.TYPE_VALUE,
    SVGClipPathElement: base_config_1.TYPE_VALUE,
    SVGComponentTransferFunctionElement: base_config_1.TYPE_VALUE,
    SVGDefsElement: base_config_1.TYPE_VALUE,
    SVGDescElement: base_config_1.TYPE_VALUE,
    SVGElementEventMap: base_config_1.TYPE,
    SVGElement: base_config_1.TYPE_VALUE,
    SVGEllipseElement: base_config_1.TYPE_VALUE,
    SVGFEBlendElement: base_config_1.TYPE_VALUE,
    SVGFEColorMatrixElement: base_config_1.TYPE_VALUE,
    SVGFEComponentTransferElement: base_config_1.TYPE_VALUE,
    SVGFECompositeElement: base_config_1.TYPE_VALUE,
    SVGFEConvolveMatrixElement: base_config_1.TYPE_VALUE,
    SVGFEDiffuseLightingElement: base_config_1.TYPE_VALUE,
    SVGFEDisplacementMapElement: base_config_1.TYPE_VALUE,
    SVGFEDistantLightElement: base_config_1.TYPE_VALUE,
    SVGFEDropShadowElement: base_config_1.TYPE_VALUE,
    SVGFEFloodElement: base_config_1.TYPE_VALUE,
    SVGFEFuncAElement: base_config_1.TYPE_VALUE,
    SVGFEFuncBElement: base_config_1.TYPE_VALUE,
    SVGFEFuncGElement: base_config_1.TYPE_VALUE,
    SVGFEFuncRElement: base_config_1.TYPE_VALUE,
    SVGFEGaussianBlurElement: base_config_1.TYPE_VALUE,
    SVGFEImageElement: base_config_1.TYPE_VALUE,
    SVGFEMergeElement: base_config_1.TYPE_VALUE,
    SVGFEMergeNodeElement: base_config_1.TYPE_VALUE,
    SVGFEMorphologyElement: base_config_1.TYPE_VALUE,
    SVGFEOffsetElement: base_config_1.TYPE_VALUE,
    SVGFEPointLightElement: base_config_1.TYPE_VALUE,
    SVGFESpecularLightingElement: base_config_1.TYPE_VALUE,
    SVGFESpotLightElement: base_config_1.TYPE_VALUE,
    SVGFETileElement: base_config_1.TYPE_VALUE,
    SVGFETurbulenceElement: base_config_1.TYPE_VALUE,
    SVGFilterElement: base_config_1.TYPE_VALUE,
    SVGFilterPrimitiveStandardAttributes: base_config_1.TYPE,
    SVGFitToViewBox: base_config_1.TYPE,
    SVGForeignObjectElement: base_config_1.TYPE_VALUE,
    SVGGElement: base_config_1.TYPE_VALUE,
    SVGGeometryElement: base_config_1.TYPE_VALUE,
    SVGGradientElement: base_config_1.TYPE_VALUE,
    SVGGraphicsElement: base_config_1.TYPE_VALUE,
    SVGImageElement: base_config_1.TYPE_VALUE,
    SVGLength: base_config_1.TYPE_VALUE,
    SVGLengthList: base_config_1.TYPE_VALUE,
    SVGLineElement: base_config_1.TYPE_VALUE,
    SVGLinearGradientElement: base_config_1.TYPE_VALUE,
    SVGMPathElement: base_config_1.TYPE_VALUE,
    SVGMarkerElement: base_config_1.TYPE_VALUE,
    SVGMaskElement: base_config_1.TYPE_VALUE,
    SVGMetadataElement: base_config_1.TYPE_VALUE,
    SVGNumber: base_config_1.TYPE_VALUE,
    SVGNumberList: base_config_1.TYPE_VALUE,
    SVGPathElement: base_config_1.TYPE_VALUE,
    SVGPatternElement: base_config_1.TYPE_VALUE,
    SVGPointList: base_config_1.TYPE_VALUE,
    SVGPolygonElement: base_config_1.TYPE_VALUE,
    SVGPolylineElement: base_config_1.TYPE_VALUE,
    SVGPreserveAspectRatio: base_config_1.TYPE_VALUE,
    SVGRadialGradientElement: base_config_1.TYPE_VALUE,
    SVGRectElement: base_config_1.TYPE_VALUE,
    SVGSVGElementEventMap: base_config_1.TYPE,
    SVGSVGElement: base_config_1.TYPE_VALUE,
    SVGScriptElement: base_config_1.TYPE_VALUE,
    SVGSetElement: base_config_1.TYPE_VALUE,
    SVGStopElement: base_config_1.TYPE_VALUE,
    SVGStringList: base_config_1.TYPE_VALUE,
    SVGStyleElement: base_config_1.TYPE_VALUE,
    SVGSwitchElement: base_config_1.TYPE_VALUE,
    SVGSymbolElement: base_config_1.TYPE_VALUE,
    SVGTSpanElement: base_config_1.TYPE_VALUE,
    SVGTests: base_config_1.TYPE,
    SVGTextContentElement: base_config_1.TYPE_VALUE,
    SVGTextElement: base_config_1.TYPE_VALUE,
    SVGTextPathElement: base_config_1.TYPE_VALUE,
    SVGTextPositioningElement: base_config_1.TYPE_VALUE,
    SVGTitleElement: base_config_1.TYPE_VALUE,
    SVGTransform: base_config_1.TYPE_VALUE,
    SVGTransformList: base_config_1.TYPE_VALUE,
    SVGURIReference: base_config_1.TYPE,
    SVGUnitTypes: base_config_1.TYPE_VALUE,
    SVGUseElement: base_config_1.TYPE_VALUE,
    SVGViewElement: base_config_1.TYPE_VALUE,
    Screen: base_config_1.TYPE_VALUE,
    ScreenOrientationEventMap: base_config_1.TYPE,
    ScreenOrientation: base_config_1.TYPE_VALUE,
    ScriptProcessorNodeEventMap: base_config_1.TYPE,
    ScriptProcessorNode: base_config_1.TYPE_VALUE,
    SecurityPolicyViolationEvent: base_config_1.TYPE_VALUE,
    Selection: base_config_1.TYPE_VALUE,
    ServiceWorkerEventMap: base_config_1.TYPE,
    ServiceWorker: base_config_1.TYPE_VALUE,
    ServiceWorkerContainerEventMap: base_config_1.TYPE,
    ServiceWorkerContainer: base_config_1.TYPE_VALUE,
    ServiceWorkerRegistrationEventMap: base_config_1.TYPE,
    ServiceWorkerRegistration: base_config_1.TYPE_VALUE,
    ShadowRootEventMap: base_config_1.TYPE,
    ShadowRoot: base_config_1.TYPE_VALUE,
    SharedWorker: base_config_1.TYPE_VALUE,
    Slottable: base_config_1.TYPE,
    SourceBufferEventMap: base_config_1.TYPE,
    SourceBuffer: base_config_1.TYPE_VALUE,
    SourceBufferListEventMap: base_config_1.TYPE,
    SourceBufferList: base_config_1.TYPE_VALUE,
    SpeechRecognitionAlternative: base_config_1.TYPE_VALUE,
    SpeechRecognitionResult: base_config_1.TYPE_VALUE,
    SpeechRecognitionResultList: base_config_1.TYPE_VALUE,
    SpeechSynthesisEventMap: base_config_1.TYPE,
    SpeechSynthesis: base_config_1.TYPE_VALUE,
    SpeechSynthesisErrorEvent: base_config_1.TYPE_VALUE,
    SpeechSynthesisEvent: base_config_1.TYPE_VALUE,
    SpeechSynthesisUtteranceEventMap: base_config_1.TYPE,
    SpeechSynthesisUtterance: base_config_1.TYPE_VALUE,
    SpeechSynthesisVoice: base_config_1.TYPE_VALUE,
    StaticRange: base_config_1.TYPE_VALUE,
    StereoPannerNode: base_config_1.TYPE_VALUE,
    Storage: base_config_1.TYPE_VALUE,
    StorageEvent: base_config_1.TYPE_VALUE,
    StorageManager: base_config_1.TYPE_VALUE,
    StyleMedia: base_config_1.TYPE,
    StyleSheet: base_config_1.TYPE_VALUE,
    StyleSheetList: base_config_1.TYPE_VALUE,
    SubmitEvent: base_config_1.TYPE_VALUE,
    SubtleCrypto: base_config_1.TYPE_VALUE,
    Text: base_config_1.TYPE_VALUE,
    TextDecoder: base_config_1.TYPE_VALUE,
    TextDecoderCommon: base_config_1.TYPE,
    TextDecoderStream: base_config_1.TYPE_VALUE,
    TextEncoder: base_config_1.TYPE_VALUE,
    TextEncoderCommon: base_config_1.TYPE,
    TextEncoderStream: base_config_1.TYPE_VALUE,
    TextMetrics: base_config_1.TYPE_VALUE,
    TextTrackEventMap: base_config_1.TYPE,
    TextTrack: base_config_1.TYPE_VALUE,
    TextTrackCueEventMap: base_config_1.TYPE,
    TextTrackCue: base_config_1.TYPE_VALUE,
    TextTrackCueList: base_config_1.TYPE_VALUE,
    TextTrackListEventMap: base_config_1.TYPE,
    TextTrackList: base_config_1.TYPE_VALUE,
    TimeRanges: base_config_1.TYPE_VALUE,
    Touch: base_config_1.TYPE_VALUE,
    TouchEvent: base_config_1.TYPE_VALUE,
    TouchList: base_config_1.TYPE_VALUE,
    TrackEvent: base_config_1.TYPE_VALUE,
    TransformStream: base_config_1.TYPE_VALUE,
    TransformStreamDefaultController: base_config_1.TYPE_VALUE,
    TransitionEvent: base_config_1.TYPE_VALUE,
    TreeWalker: base_config_1.TYPE_VALUE,
    UIEvent: base_config_1.TYPE_VALUE,
    URL: base_config_1.TYPE_VALUE,
    webkitURL: base_config_1.TYPE_VALUE,
    URLSearchParams: base_config_1.TYPE_VALUE,
    VTTCue: base_config_1.TYPE_VALUE,
    VTTRegion: base_config_1.TYPE_VALUE,
    ValidityState: base_config_1.TYPE_VALUE,
    VideoColorSpace: base_config_1.TYPE_VALUE,
    VideoPlaybackQuality: base_config_1.TYPE_VALUE,
    VisualViewportEventMap: base_config_1.TYPE,
    VisualViewport: base_config_1.TYPE_VALUE,
    WEBGL_color_buffer_float: base_config_1.TYPE,
    WEBGL_compressed_texture_astc: base_config_1.TYPE,
    WEBGL_compressed_texture_etc: base_config_1.TYPE,
    WEBGL_compressed_texture_etc1: base_config_1.TYPE,
    WEBGL_compressed_texture_s3tc: base_config_1.TYPE,
    WEBGL_compressed_texture_s3tc_srgb: base_config_1.TYPE,
    WEBGL_debug_renderer_info: base_config_1.TYPE,
    WEBGL_debug_shaders: base_config_1.TYPE,
    WEBGL_depth_texture: base_config_1.TYPE,
    WEBGL_draw_buffers: base_config_1.TYPE,
    WEBGL_lose_context: base_config_1.TYPE,
    WEBGL_multi_draw: base_config_1.TYPE,
    WaveShaperNode: base_config_1.TYPE_VALUE,
    WebGL2RenderingContext: base_config_1.TYPE_VALUE,
    WebGL2RenderingContextBase: base_config_1.TYPE,
    WebGL2RenderingContextOverloads: base_config_1.TYPE,
    WebGLActiveInfo: base_config_1.TYPE_VALUE,
    WebGLBuffer: base_config_1.TYPE_VALUE,
    WebGLContextEvent: base_config_1.TYPE_VALUE,
    WebGLFramebuffer: base_config_1.TYPE_VALUE,
    WebGLProgram: base_config_1.TYPE_VALUE,
    WebGLQuery: base_config_1.TYPE_VALUE,
    WebGLRenderbuffer: base_config_1.TYPE_VALUE,
    WebGLRenderingContext: base_config_1.TYPE_VALUE,
    WebGLRenderingContextBase: base_config_1.TYPE,
    WebGLRenderingContextOverloads: base_config_1.TYPE,
    WebGLSampler: base_config_1.TYPE_VALUE,
    WebGLShader: base_config_1.TYPE_VALUE,
    WebGLShaderPrecisionFormat: base_config_1.TYPE_VALUE,
    WebGLSync: base_config_1.TYPE_VALUE,
    WebGLTexture: base_config_1.TYPE_VALUE,
    WebGLTransformFeedback: base_config_1.TYPE_VALUE,
    WebGLUniformLocation: base_config_1.TYPE_VALUE,
    WebGLVertexArrayObject: base_config_1.TYPE_VALUE,
    WebGLVertexArrayObjectOES: base_config_1.TYPE,
    WebSocketEventMap: base_config_1.TYPE,
    WebSocket: base_config_1.TYPE_VALUE,
    WheelEvent: base_config_1.TYPE_VALUE,
    WindowEventMap: base_config_1.TYPE,
    Window: base_config_1.TYPE_VALUE,
    WindowEventHandlersEventMap: base_config_1.TYPE,
    WindowEventHandlers: base_config_1.TYPE,
    WindowLocalStorage: base_config_1.TYPE,
    WindowOrWorkerGlobalScope: base_config_1.TYPE,
    WindowSessionStorage: base_config_1.TYPE,
    WorkerEventMap: base_config_1.TYPE,
    Worker: base_config_1.TYPE_VALUE,
    Worklet: base_config_1.TYPE_VALUE,
    WritableStream: base_config_1.TYPE_VALUE,
    WritableStreamDefaultController: base_config_1.TYPE_VALUE,
    WritableStreamDefaultWriter: base_config_1.TYPE_VALUE,
    XMLDocument: base_config_1.TYPE_VALUE,
    XMLHttpRequestEventMap: base_config_1.TYPE,
    XMLHttpRequest: base_config_1.TYPE_VALUE,
    XMLHttpRequestEventTargetEventMap: base_config_1.TYPE,
    XMLHttpRequestEventTarget: base_config_1.TYPE_VALUE,
    XMLHttpRequestUpload: base_config_1.TYPE_VALUE,
    XMLSerializer: base_config_1.TYPE_VALUE,
    XPathEvaluator: base_config_1.TYPE_VALUE,
    XPathEvaluatorBase: base_config_1.TYPE,
    XPathExpression: base_config_1.TYPE_VALUE,
    XPathResult: base_config_1.TYPE_VALUE,
    XSLTProcessor: base_config_1.TYPE_VALUE,
    Console: base_config_1.TYPE,
    CSS: base_config_1.TYPE_VALUE,
    WebAssembly: base_config_1.TYPE_VALUE,
    BlobCallback: base_config_1.TYPE,
    CustomElementConstructor: base_config_1.TYPE,
    DecodeErrorCallback: base_config_1.TYPE,
    DecodeSuccessCallback: base_config_1.TYPE,
    ErrorCallback: base_config_1.TYPE,
    FileCallback: base_config_1.TYPE,
    FileSystemEntriesCallback: base_config_1.TYPE,
    FileSystemEntryCallback: base_config_1.TYPE,
    FrameRequestCallback: base_config_1.TYPE,
    FunctionStringCallback: base_config_1.TYPE,
    IdleRequestCallback: base_config_1.TYPE,
    IntersectionObserverCallback: base_config_1.TYPE,
    LockGrantedCallback: base_config_1.TYPE,
    MediaSessionActionHandler: base_config_1.TYPE,
    MutationCallback: base_config_1.TYPE,
    NotificationPermissionCallback: base_config_1.TYPE,
    OnBeforeUnloadEventHandlerNonNull: base_config_1.TYPE,
    OnErrorEventHandlerNonNull: base_config_1.TYPE,
    PerformanceObserverCallback: base_config_1.TYPE,
    PositionCallback: base_config_1.TYPE,
    PositionErrorCallback: base_config_1.TYPE,
    QueuingStrategySize: base_config_1.TYPE,
    RTCPeerConnectionErrorCallback: base_config_1.TYPE,
    RTCSessionDescriptionCallback: base_config_1.TYPE,
    RemotePlaybackAvailabilityCallback: base_config_1.TYPE,
    ResizeObserverCallback: base_config_1.TYPE,
    TransformerFlushCallback: base_config_1.TYPE,
    TransformerStartCallback: base_config_1.TYPE,
    TransformerTransformCallback: base_config_1.TYPE,
    UnderlyingSinkAbortCallback: base_config_1.TYPE,
    UnderlyingSinkCloseCallback: base_config_1.TYPE,
    UnderlyingSinkStartCallback: base_config_1.TYPE,
    UnderlyingSinkWriteCallback: base_config_1.TYPE,
    UnderlyingSourceCancelCallback: base_config_1.TYPE,
    UnderlyingSourcePullCallback: base_config_1.TYPE,
    UnderlyingSourceStartCallback: base_config_1.TYPE,
    VideoFrameRequestCallback: base_config_1.TYPE,
    VoidFunction: base_config_1.TYPE,
    HTMLElementTagNameMap: base_config_1.TYPE,
    HTMLElementDeprecatedTagNameMap: base_config_1.TYPE,
    SVGElementTagNameMap: base_config_1.TYPE,
    MathMLElementTagNameMap: base_config_1.TYPE,
    ElementTagNameMap: base_config_1.TYPE,
    AlgorithmIdentifier: base_config_1.TYPE,
    BigInteger: base_config_1.TYPE,
    BinaryData: base_config_1.TYPE,
    BlobPart: base_config_1.TYPE,
    BodyInit: base_config_1.TYPE,
    BufferSource: base_config_1.TYPE,
    COSEAlgorithmIdentifier: base_config_1.TYPE,
    CSSNumberish: base_config_1.TYPE,
    CanvasImageSource: base_config_1.TYPE,
    ClipboardItemData: base_config_1.TYPE,
    ClipboardItems: base_config_1.TYPE,
    ConstrainBoolean: base_config_1.TYPE,
    ConstrainDOMString: base_config_1.TYPE,
    ConstrainDouble: base_config_1.TYPE,
    ConstrainULong: base_config_1.TYPE,
    DOMHighResTimeStamp: base_config_1.TYPE,
    EpochTimeStamp: base_config_1.TYPE,
    EventListenerOrEventListenerObject: base_config_1.TYPE,
    Float32List: base_config_1.TYPE,
    FormDataEntryValue: base_config_1.TYPE,
    GLbitfield: base_config_1.TYPE,
    GLboolean: base_config_1.TYPE,
    GLclampf: base_config_1.TYPE,
    GLenum: base_config_1.TYPE,
    GLfloat: base_config_1.TYPE,
    GLint: base_config_1.TYPE,
    GLint64: base_config_1.TYPE,
    GLintptr: base_config_1.TYPE,
    GLsizei: base_config_1.TYPE,
    GLsizeiptr: base_config_1.TYPE,
    GLuint: base_config_1.TYPE,
    GLuint64: base_config_1.TYPE,
    HTMLOrSVGImageElement: base_config_1.TYPE,
    HTMLOrSVGScriptElement: base_config_1.TYPE,
    HashAlgorithmIdentifier: base_config_1.TYPE,
    HeadersInit: base_config_1.TYPE,
    IDBValidKey: base_config_1.TYPE,
    ImageBitmapSource: base_config_1.TYPE,
    Int32List: base_config_1.TYPE,
    LineAndPositionSetting: base_config_1.TYPE,
    MediaProvider: base_config_1.TYPE,
    MessageEventSource: base_config_1.TYPE,
    MutationRecordType: base_config_1.TYPE,
    NamedCurve: base_config_1.TYPE,
    OffscreenRenderingContext: base_config_1.TYPE,
    OnBeforeUnloadEventHandler: base_config_1.TYPE,
    OnErrorEventHandler: base_config_1.TYPE,
    PerformanceEntryList: base_config_1.TYPE,
    ReadableStreamController: base_config_1.TYPE,
    ReadableStreamReadResult: base_config_1.TYPE,
    ReadableStreamReader: base_config_1.TYPE,
    RenderingContext: base_config_1.TYPE,
    RequestInfo: base_config_1.TYPE,
    TexImageSource: base_config_1.TYPE,
    TimerHandler: base_config_1.TYPE,
    Transferable: base_config_1.TYPE,
    Uint32List: base_config_1.TYPE,
    VibratePattern: base_config_1.TYPE,
    WindowProxy: base_config_1.TYPE,
    XMLHttpRequestBodyInit: base_config_1.TYPE,
    AlignSetting: base_config_1.TYPE,
    AnimationPlayState: base_config_1.TYPE,
    AnimationReplaceState: base_config_1.TYPE,
    AppendMode: base_config_1.TYPE,
    AttestationConveyancePreference: base_config_1.TYPE,
    AudioContextLatencyCategory: base_config_1.TYPE,
    AudioContextState: base_config_1.TYPE,
    AuthenticatorAttachment: base_config_1.TYPE,
    AuthenticatorTransport: base_config_1.TYPE,
    AutoKeyword: base_config_1.TYPE,
    AutomationRate: base_config_1.TYPE,
    BinaryType: base_config_1.TYPE,
    BiquadFilterType: base_config_1.TYPE,
    CanPlayTypeResult: base_config_1.TYPE,
    CanvasDirection: base_config_1.TYPE,
    CanvasFillRule: base_config_1.TYPE,
    CanvasFontKerning: base_config_1.TYPE,
    CanvasFontStretch: base_config_1.TYPE,
    CanvasFontVariantCaps: base_config_1.TYPE,
    CanvasLineCap: base_config_1.TYPE,
    CanvasLineJoin: base_config_1.TYPE,
    CanvasTextAlign: base_config_1.TYPE,
    CanvasTextBaseline: base_config_1.TYPE,
    CanvasTextRendering: base_config_1.TYPE,
    ChannelCountMode: base_config_1.TYPE,
    ChannelInterpretation: base_config_1.TYPE,
    ClientTypes: base_config_1.TYPE,
    ColorGamut: base_config_1.TYPE,
    ColorSpaceConversion: base_config_1.TYPE,
    CompositeOperation: base_config_1.TYPE,
    CompositeOperationOrAuto: base_config_1.TYPE,
    CredentialMediationRequirement: base_config_1.TYPE,
    DOMParserSupportedType: base_config_1.TYPE,
    DirectionSetting: base_config_1.TYPE,
    DisplayCaptureSurfaceType: base_config_1.TYPE,
    DistanceModelType: base_config_1.TYPE,
    DocumentReadyState: base_config_1.TYPE,
    DocumentVisibilityState: base_config_1.TYPE,
    EndOfStreamError: base_config_1.TYPE,
    EndingType: base_config_1.TYPE,
    FileSystemHandleKind: base_config_1.TYPE,
    FillMode: base_config_1.TYPE,
    FontDisplay: base_config_1.TYPE,
    FontFaceLoadStatus: base_config_1.TYPE,
    FontFaceSetLoadStatus: base_config_1.TYPE,
    FullscreenNavigationUI: base_config_1.TYPE,
    GamepadHapticActuatorType: base_config_1.TYPE,
    GamepadMappingType: base_config_1.TYPE,
    GlobalCompositeOperation: base_config_1.TYPE,
    HdrMetadataType: base_config_1.TYPE,
    IDBCursorDirection: base_config_1.TYPE,
    IDBRequestReadyState: base_config_1.TYPE,
    IDBTransactionDurability: base_config_1.TYPE,
    IDBTransactionMode: base_config_1.TYPE,
    ImageOrientation: base_config_1.TYPE,
    ImageSmoothingQuality: base_config_1.TYPE,
    InsertPosition: base_config_1.TYPE,
    IterationCompositeOperation: base_config_1.TYPE,
    KeyFormat: base_config_1.TYPE,
    KeyType: base_config_1.TYPE,
    KeyUsage: base_config_1.TYPE,
    LineAlignSetting: base_config_1.TYPE,
    LockMode: base_config_1.TYPE,
    MIDIPortConnectionState: base_config_1.TYPE,
    MIDIPortDeviceState: base_config_1.TYPE,
    MIDIPortType: base_config_1.TYPE,
    MediaDecodingType: base_config_1.TYPE,
    MediaDeviceKind: base_config_1.TYPE,
    MediaEncodingType: base_config_1.TYPE,
    MediaKeyMessageType: base_config_1.TYPE,
    MediaKeySessionClosedReason: base_config_1.TYPE,
    MediaKeySessionType: base_config_1.TYPE,
    MediaKeyStatus: base_config_1.TYPE,
    MediaKeysRequirement: base_config_1.TYPE,
    MediaSessionAction: base_config_1.TYPE,
    MediaSessionPlaybackState: base_config_1.TYPE,
    MediaStreamTrackState: base_config_1.TYPE,
    NavigationTimingType: base_config_1.TYPE,
    NotificationDirection: base_config_1.TYPE,
    NotificationPermission: base_config_1.TYPE,
    OffscreenRenderingContextId: base_config_1.TYPE,
    OrientationLockType: base_config_1.TYPE,
    OrientationType: base_config_1.TYPE,
    OscillatorType: base_config_1.TYPE,
    OverSampleType: base_config_1.TYPE,
    PanningModelType: base_config_1.TYPE,
    PaymentComplete: base_config_1.TYPE,
    PermissionName: base_config_1.TYPE,
    PermissionState: base_config_1.TYPE,
    PlaybackDirection: base_config_1.TYPE,
    PositionAlignSetting: base_config_1.TYPE,
    PredefinedColorSpace: base_config_1.TYPE,
    PremultiplyAlpha: base_config_1.TYPE,
    PresentationStyle: base_config_1.TYPE,
    PublicKeyCredentialType: base_config_1.TYPE,
    PushEncryptionKeyName: base_config_1.TYPE,
    RTCBundlePolicy: base_config_1.TYPE,
    RTCDataChannelState: base_config_1.TYPE,
    RTCDegradationPreference: base_config_1.TYPE,
    RTCDtlsTransportState: base_config_1.TYPE,
    RTCEncodedVideoFrameType: base_config_1.TYPE,
    RTCErrorDetailType: base_config_1.TYPE,
    RTCIceCandidateType: base_config_1.TYPE,
    RTCIceComponent: base_config_1.TYPE,
    RTCIceConnectionState: base_config_1.TYPE,
    RTCIceGathererState: base_config_1.TYPE,
    RTCIceGatheringState: base_config_1.TYPE,
    RTCIceProtocol: base_config_1.TYPE,
    RTCIceTcpCandidateType: base_config_1.TYPE,
    RTCIceTransportPolicy: base_config_1.TYPE,
    RTCIceTransportState: base_config_1.TYPE,
    RTCPeerConnectionState: base_config_1.TYPE,
    RTCPriorityType: base_config_1.TYPE,
    RTCRtcpMuxPolicy: base_config_1.TYPE,
    RTCRtpTransceiverDirection: base_config_1.TYPE,
    RTCSctpTransportState: base_config_1.TYPE,
    RTCSdpType: base_config_1.TYPE,
    RTCSignalingState: base_config_1.TYPE,
    RTCStatsIceCandidatePairState: base_config_1.TYPE,
    RTCStatsType: base_config_1.TYPE,
    ReadableStreamReaderMode: base_config_1.TYPE,
    ReadableStreamType: base_config_1.TYPE,
    ReadyState: base_config_1.TYPE,
    RecordingState: base_config_1.TYPE,
    ReferrerPolicy: base_config_1.TYPE,
    RemotePlaybackState: base_config_1.TYPE,
    RequestCache: base_config_1.TYPE,
    RequestCredentials: base_config_1.TYPE,
    RequestDestination: base_config_1.TYPE,
    RequestMode: base_config_1.TYPE,
    RequestRedirect: base_config_1.TYPE,
    ResidentKeyRequirement: base_config_1.TYPE,
    ResizeObserverBoxOptions: base_config_1.TYPE,
    ResizeQuality: base_config_1.TYPE,
    ResponseType: base_config_1.TYPE,
    ScrollBehavior: base_config_1.TYPE,
    ScrollLogicalPosition: base_config_1.TYPE,
    ScrollRestoration: base_config_1.TYPE,
    ScrollSetting: base_config_1.TYPE,
    SecurityPolicyViolationEventDisposition: base_config_1.TYPE,
    SelectionMode: base_config_1.TYPE,
    ServiceWorkerState: base_config_1.TYPE,
    ServiceWorkerUpdateViaCache: base_config_1.TYPE,
    ShadowRootMode: base_config_1.TYPE,
    SlotAssignmentMode: base_config_1.TYPE,
    SpeechSynthesisErrorCode: base_config_1.TYPE,
    TextTrackKind: base_config_1.TYPE,
    TextTrackMode: base_config_1.TYPE,
    TouchType: base_config_1.TYPE,
    TransferFunction: base_config_1.TYPE,
    UserVerificationRequirement: base_config_1.TYPE,
    VideoColorPrimaries: base_config_1.TYPE,
    VideoFacingModeEnum: base_config_1.TYPE,
    VideoMatrixCoefficients: base_config_1.TYPE,
    VideoTransferCharacteristics: base_config_1.TYPE,
    WebGLPowerPreference: base_config_1.TYPE,
    WorkerType: base_config_1.TYPE,
    XMLHttpRequestResponseType: base_config_1.TYPE,
};
//# sourceMappingURL=dom.js.map