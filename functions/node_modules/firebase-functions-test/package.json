{"name": "firebase-functions-test", "version": "3.4.1", "description": "A testing companion to firebase-functions.", "main": "lib/index.js", "scripts": {"build": "npm i && node_modules/.bin/tsc -p tsconfig.release.json", "build:pack": "npm prune --production && rm -rf lib && npm install && node_modules/.bin/tsc -p tsconfig.release.json && npm pack && npm install", "build:release": "npm ci --production && npm install --no-save typescript firebase-functions firebase-admin && node_modules/.bin/tsc -p tsconfig.release.json", "lint": "node_modules/.bin/tslint src/{**/*,*}.ts spec/{**/*,*}.ts", "pretest": "node_modules/.bin/tsc", "test": "mocha .tmp/spec/index.spec.js", "posttest": "npm run lint && rm -rf .tmp", "preintegrationTest": "node_modules/.bin/tsc", "integrationTest": "firebase emulators:exec --project=not-a-project --only firestore 'mocha .tmp/spec/integration/**/*.spec.js'", "postintegrationTest": "rm -rf .tmp", "format": "prettier --check '**/*.{json,ts,yml,yaml}'", "format:fix": "prettier --write '**/*.{json,ts,yml,yaml}'"}, "repository": {"type": "git", "url": "https://github.com/firebase/firebase-functions-test.git"}, "keywords": ["firebase", "functions", "google", "cloud", "test"], "author": "Firebase Team", "license": "MIT", "bugs": {"url": "https://github.com/firebase/firebase-functions-test/issues"}, "publishConfig": {"registry": "https://wombat-dressing-room.appspot.com"}, "homepage": "https://github.com/firebase/firebase-functions-test#readme", "dependencies": {"@types/lodash": "^4.14.104", "lodash": "^4.17.5", "ts-deepmerge": "^2.0.1"}, "devDependencies": {"@types/chai": "~4.2.4", "@types/express": "4.17.8", "@types/mocha": "^5.2.7", "chai": "^4.2.0", "firebase-admin": "^12.0.0", "firebase-functions": "^4.9.0", "firebase-tools": "^8.9.2", "mocha": "^6.2.2", "prettier": "^1.19.1", "sinon": "^7.5.0", "tslint": "^5.20.0", "typescript": "^4.2.5"}, "peerDependencies": {"firebase-admin": "^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0 || ^12.0.0 || ^13.0.0", "firebase-functions": ">=4.9.0", "jest": ">=28.0.0"}, "engines": {"node": ">=14.0.0"}, "typings": "lib/index.d.ts"}