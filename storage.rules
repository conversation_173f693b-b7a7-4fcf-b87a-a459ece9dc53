rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Users can upload and read their own profile images
    match /users/{userId}/profile/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can upload and read their own meal images
    match /users/{userId}/meals/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public read access to app assets
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if false; // Only admins can write
    }
  }
}
