# EasyDietAI

AI-powered diet planning application with personalized meal recommendations.

## Features

- 🤖 AI-powered meal planning
- 🔥 Firebase authentication (Google, email/password, guest)
- 📱 Responsive design with Arabic support
- 🎨 Modern Material Design 3 UI
- 📊 Nutrition tracking and analytics
- 💳 In-app subscription management
- 🔔 Local notifications
- 📱 Offline support with Hive

## Architecture

This project follows a **feature-first architecture** with clean architecture principles:

```
lib/
├── core/                    # Core utilities and configurations
│   ├── config/             # App configuration
│   ├── error/              # Error handling
│   ├── router/             # Navigation and routing
│   ├── theme/              # App theming
│   └── utils/              # Utilities and extensions
├── features/               # Feature modules
│   ├── auth/               # Authentication feature
│   ├── meal_planning/      # Meal planning feature
│   ├── subscription/       # Subscription management
│   └── user_profile/       # User profile management
├── shared/                 # Shared components
│   ├── models/             # Shared data models
│   ├── providers/          # Shared Riverpod providers
│   └── widgets/            # Reusable UI components
├── generated/              # Generated files (l10n, etc.)
└── main.dart              # App entry point
```

## Tech Stack

### State Management
- **Riverpod** - Modern state management with code generation
- **Hooks Riverpod** - React-like hooks for Flutter

### Networking
- **Dio** - HTTP client with interceptors
- **Retrofit** - Type-safe REST client

### Database & Storage
- **Hive** - Fast, lightweight NoSQL database
- **Shared Preferences** - Simple key-value storage
- **Firebase Firestore** - Cloud database

### Authentication
- **Firebase Auth** - Authentication service
- **Google Sign-In** - Google authentication

### UI & Navigation
- **Go Router** - Declarative routing
- **Flutter ScreenUtil** - Screen adaptation
- **Google Fonts** - Custom fonts (Cairo for Arabic)
- **Cached Network Image** - Image caching

### Localization
- **Flutter Intl** - Internationalization
- **Arabic & English** support

### Code Generation
- **Freezed** - Immutable data classes
- **JSON Serializable** - JSON serialization
- **Riverpod Generator** - Provider code generation

### Development Tools
- **Very Good Analysis** - Strict linting rules
- **Logger** - Structured logging
- **Build Runner** - Code generation

## Getting Started

### Prerequisites

- Flutter SDK (>=3.1.0)
- Dart SDK (>=3.1.0)
- Firebase project setup
- Android Studio / VS Code

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd easydietai
```

2. Install dependencies:
```bash
flutter pub get
```

3. Generate code:
```bash
flutter packages pub run build_runner build
```

4. Generate localization:
```bash
flutter gen-l10n
```

5. Run the app:
```bash
flutter run
```

### Firebase Setup

1. Create a Firebase project
2. Enable Authentication (Email/Password, Google)
3. Enable Firestore Database
4. Add your `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)

### Code Generation

Run code generation when you modify:
- Models with `@freezed` annotation
- Providers with `@riverpod` annotation
- API clients with `@RestApi` annotation

```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## Project Structure Details

### Core Layer
- **Config**: App-wide configuration and constants
- **Error**: Centralized error handling with Failure classes
- **Router**: Navigation setup with Go Router
- **Theme**: Material Design 3 theming with Arabic support
- **Utils**: Extensions, helpers, and utilities

### Feature Layer
Each feature follows clean architecture:
- **Data**: Data sources, repositories, models
- **Domain**: Business logic, use cases, entities
- **Presentation**: UI, controllers, providers

### Shared Layer
- **Models**: Data models used across features
- **Providers**: Global state providers
- **Widgets**: Reusable UI components

## Development Guidelines

### State Management
- Use Riverpod providers for state management
- Prefer `@riverpod` annotation for code generation
- Use `ConsumerWidget` for widgets that need providers

### Styling
- Follow Material Design 3 guidelines
- Use theme colors from `AppColors`
- Support both light and dark themes
- Ensure RTL support for Arabic

### Error Handling
- Use `Failure` classes for error representation
- Implement proper error boundaries
- Show user-friendly error messages

### Testing
- Write unit tests for business logic
- Write widget tests for UI components
- Use integration tests for critical flows

## Contributing

1. Follow the existing code style
2. Write tests for new features
3. Update documentation
4. Use conventional commits

## License

This project is licensed under the MIT License.
