{"projectName": "EasyDietAI", "language": "dart", "styleGuide": {"stateManagement": "riverpod", "httpClient": "dio", "architecture": "feature-first", "localization": "intl with default Arabic", "authentication": "FirebaseAuth (Google, email/password, guest)", "notifications": "flutter_local_notifications", "subscription": "in_app_purchase"}, "aiHints": ["Use Riverpod hooks for state access", "Use Firestore for syncing persistent data", "Use named models like MealPlan, UserProfile"]}