# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release


# Flutter/Dart/Pub related
.packages
.pub-cache
build/
android/.gradle
ios/Pods/
**/*.iml
.env

# Generated files
**/*.g.dart
**/*.freezed.dart
**/*.gr.dart
**/*.config.dart

# Firebase
google-services.json
GoogleService-Info.plist
firebase_options.dart

# Coverage
coverage/

# IDE
.vscode/
.idea/
